/* eslint-disable indent, prettier/prettier */

// realtime database config
const realTimeDir = "counter";

// local configs
const datasetKeys = {
    tltc: "tltc", // 文學外譯房
};
// local utils
const realTimeDataKey = (key) =>
    process.env.REALTIME_DATABASE_MODE === "production"
        ? key
        : `${key}${
              process.env.REALTIME_DATABASE_MODE === "production_nmtlGov"
                  ? "_nmtlGov"
                  : "2"
          }`;

// tltc 文學外譯房
const tltc = {
    rtDbKey: realTimeDataKey(datasetKeys.tltc),
    hotBooks: {
        api: `/updateHotSearch/${datasetKeys.tltc}/publication/list/3.0?limit=-1&offset=0&ds=${datasetKeys.tltc}`,
    },
    hotPerson: {
        api: `/updateHotSearch/${datasetKeys.tltc}/person/list/3.0?limit=-1&offset=0&ds=${datasetKeys.tltc}`,
    },
};

const subPlatformConfigs = [tltc];

module.exports = { realTimeDir, datasetKeys, subPlatformConfigs };
