const { getClientIp } = require("./daoyi-request-ip");
const { getFullWorkAllowIps } = require("../handleAuth/apiObj");
const { RESPONSE_OK } = require("../../config/config");

module.exports = {
    async publicIp(req, res) {
        const clientIp = getClientIp(req);
        const isAllowed = getFullWorkAllowIps().indexOf(clientIp) >= 0;
        return res.status(RESPONSE_OK).send({ isAllowed, ip: clientIp });
    },
};
