const { graphIri } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

// const copySparql = (oldGraph, newGraph, srcId) => {
//     return `
//         INSERT {
//             GRAPH ${newGraph} {
//                 nmtl:${srcId} ?p ?o .
//             }
//         }
//         WHERE {
//             GRAPH ${oldGraph} {
//                 {
//                    nmtl:${srcId} ?p ?o .
//                 }
//             }
//         }
//         `;
// };

// 複製資料到其他graph，並將原本無zh lang tag 的label加上zh lang tag，且移除無lang tag 的 label
const copySparql = (oldGraph, newGraph, srcId) => {
    return `
            INSERT {
                GRAPH  ${newGraph} {
                    nmtl:${srcId} ?p ?o .
                }
            }
            WHERE {
                GRAPH ${oldGraph} {
                    nmtl:${srcId} ?p ?o .
                }
            };
            
            DELETE {
                GRAPH  ${newGraph} {
                    nmtl:${srcId} rdfs:label ?oldLabel .
                }
            }
            WHERE {
                GRAPH  ${newGraph} {
                    nmtl:${srcId} rdfs:label ?oldLabel .
                    FILTER(lang(?oldLabel) = "")
                }
            };
            
            INSERT {
                GRAPH ${newGraph} {
                    nmtl:${srcId} rdfs:label ?newLabel .
                }
            }
            WHERE {
                GRAPH ${oldGraph} {
                    nmtl:${srcId} rdfs:label ?label .
                }
                BIND(
                    IF(
                        BOUND(?label) && lang(?label) != "zh",
                        STRLANG(STR(?label), "zh"),
                        ?label
                    ) AS ?newLabel
                )
                FILTER(BOUND(?newLabel) && lang(?newLabel) = "zh")
            }
          `;
};

const copyInstances = (entrySrc, entryDst) => {
    const _queryStr = copySparql(
        graphIri(entrySrc.value),
        graphIri(entryDst.value),
        entryDst.srcId
    );

    return doSaprqlUpdate(_queryStr);
};

exports.copyInstances20 = (entrySrc, entryDst, callback) => {
    Promise.all([copyInstances(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
