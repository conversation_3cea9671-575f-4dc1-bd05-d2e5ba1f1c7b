const { verifyToken } = require("../handleAuth/auth");
// const { getReservedCurId, getMaxId } = require("../handleAuth/apiObj");
// const { classPrefix } = require("./services/sparql/classPrefix");
// const { doSaprqlQueryAsync } = require("./services/common/sparql-common");
const createApiList = require("./services/crud/createApis");
const {
    ERROR_NO_PARAM_VER,
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_API_VER_NOT_EXIST,
    ERROR_NO_PAYLOAD,
    ERROR_LACK_OF_PARAMS,
    DEFAULT_CREATE_GRAPH,
    ERROR_WRONG_PARAMS,
} = require("../../config/config");

const { getReservedNewId } = require("./services/common/getReservedNewId");

// const getReservedNewId = async (classType) => {
//     if (!classType) {
//         return null;
//     }
//     // 先確認這個 classType 是否有 prefix
//     const found = classPrefix.filter((cl) => cl.eventType === classType);
//     if (!found || found.length === 0) {
//         return null;
//     }
//
//     const regPrefix = new RegExp(`\\[prefix\\]`, "g");
//     const regClassName = new RegExp(`\\[classname\\]`, "g");
//
//     // 取回目前最大值
//     const curMaxData = await doSaprqlQueryAsync(
//         getReservedCurId().replace(regPrefix, found[0].prefix),
//         0,
//         0
//     );
//
//     const maxValueId = curMaxData?.results.bindings[0].maxValue.value;
//     if (!maxValueId || maxValueId === "0") {
//         // FIXME: Vincent, 希望可以動態計算最大值並填入 max_xxx
//         // 沒有這個 property 存在，試著取最大值
//         const oldMaxData = await doSaprqlQueryAsync(
//             getMaxId()
//                 .replace(regClassName, classType)
//                 .replace(regPrefix, found[0].prefix),
//             0,
//             0
//         );
//
//         const curMaxId = oldMaxData?.results.bindings[0]?.maxNum.value;
//         if (curMaxId && curMaxId !== "") {
//             // 寫入目前最大值
//             // 避免 ID 被 cache，所以帶入 random 值
//             await createApiList.newInstance["2.0"](
//                 { prefix: found[0].prefix, number: curMaxId },
//                 () => {}
//             );
//         }
//     }
//
//     // 避免 ID 被 cache，所以帶入 random 值
//     await createApiList.newInstance["2.0"](
//         { prefix: found[0].prefix, number: "1" },
//         () => {}
//     );
//
//     // 取回目前最大值
//     const resData = await doSaprqlQueryAsync(
//         getReservedCurId().replace(regPrefix, found[0].prefix),
//         0,
//         0
//     );
//
//     const newValueId = resData.results.bindings[0].maxValue.value;
//     // found.eventType
//     return `${found[0].prefix}${newValueId}`;
// };

async function sparqlCreate(req, res) {
    const authToken = req.headers.authorization;
    const { entry } = req.body;

    // The update data is existing or not.
    if (!entry) {
        return res.status(400).send(ERROR_NO_PAYLOAD);
    }

    // The API has to have the version.
    const { ver } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }

    // The API method is not correct.
    const apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(400).send(ERROR_API_METHOD);
    }

    // The API method doesn't exist.
    const apiDef = createApiList[apiMethod];
    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    // parameters required
    const lackParms = apiDef.required.find((r) => {
        return !entry.hasOwnProperty(r);
    });
    if (lackParms) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    if (apiMethod === "generic") {
        const { srcId, graph, classType } = entry;
        if (!classType || classType === "") {
            return res.status(400).send(ERROR_LACK_OF_PARAMS);
        }
        if (!graph || graph === "") {
            entry["graph"] = DEFAULT_CREATE_GRAPH;
        }
        if (!srcId || srcId === "") {
            entry["srcId"] = await getReservedNewId(classType);
        }
        // 如果 class 不存在
        if (!entry["graph"] || !entry["srcId"]) {
            return res.status(400).send(ERROR_WRONG_PARAMS);
        }
    }

    await verifyToken(authToken)
        .then(async () => {
            await apiVer(entry, (response, error) => {
                if (error) {
                    return res.status(400).send({ error });
                }
                const foundError = response.find((r) => {
                    return r.status !== 200;
                });

                if (foundError) {
                    return res
                        .status(foundError.status)
                        .send({ error: foundError.statusText });
                }
                if (res.headersSent !== true) {
                    return res.status(200).send({ data: "OK" });
                }
            });
        })
        .catch((error) => {
            res.status(400).send({ error });
        });
}

module.exports = sparqlCreate;
