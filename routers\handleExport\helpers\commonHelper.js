const exportConfig = require("../../../config/config.export");

const commonHelper = {
    safeGetFirstEle: (list) => {
        return Array.isArray(list) && list.length > 0 ? list[0] : list || "";
    },
    getCurDate: () => {
        const datetime = new Date();
        return datetime.toISOString().slice(0, 10);
    },
    isArrayElementInArray: (arr1, arr2) => {
        return arr1.some((r) => arr2.includes(r));
    },
    getSubValueProperty: () => {
        return Object.keys(exportConfig.SubValues);
    },
    getSubValueType: (type) => {
        const key = Object.keys(exportConfig.SubValues).filter(
            (k) => exportConfig.SubValues[k]["type"] === type[0]
        )[0];

        if (key in exportConfig.SubValues) {
            return { ...exportConfig.SubValues[key], key };
        }
        return {};
    },
};

module.exports = commonHelper;
