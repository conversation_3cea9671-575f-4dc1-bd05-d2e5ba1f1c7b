/* eslint-disable indent, prettier/prettier */
const firebase = require("../../config/config.firebase");

const realtimeDb = firebase.database();

// utils
const firebaseHelper = require("../handleExport/helpers/firebaseHelper");
const errorHelper = require("../handleExport/helpers/errorHelper");
const { getApi } = require("../handleAuth/apiObj");
const {
    doMakeApiQuery,
    doMakeCountQuery,
} = require("../handleSparql/services/common/sparql-common");
const {
    map2SparqlLang,
    replaceQueryParams,
} = require("../handleSparql/services/common/common");

// configs
const { realTimeDir, subPlatformConfigs } = require("./config");
const {
    ERROR_RESPONSE,
    ERROR_WRONG_PARAMS,
    ERROR_API_NOT_EXIST,
    ERROR_API_VER_NOT_EXIST,
} = require("../../config/config");

// local configs
const DEFAULT_LANG = "zh-tw";

const realTimeKeys = {
    hotBooks: "hotBooks", // tltc
    hotPerson: "hotPerson", // tltc
};

const handleSparqlRead = async ({ res, api }) => {
    if (!api) {
        return;
    }

    const language = map2SparqlLang(DEFAULT_LANG);

    const regex =
        /^\/(?<apiMethod>[\w\/]+)\/(?<ver>[\d.]+)\?(?:.*?limit=(?<limit>-?\d+))?(?:.*?offset=(?<offset>-?\d+))?(?:.*?ds=(?<ds>[\w]+))?/;

    const { apiMethod, ver, limit, offset, ds = "" } = api.match(regex)?.groups;

    // The API method doesn't exist.
    const apiDef = getApi(apiMethod);

    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    let apiQuery = replaceQueryParams(
        apiVer.query,
        { limit, offset, ds },
        apiVer.key,
        language
    );
    if (apiQuery === "") {
        return res.status(400).send(ERROR_WRONG_PARAMS);
    }

    const getFueskiData = await Promise.all([
        doMakeApiQuery(apiQuery, limit, offset),
        doMakeCountQuery(apiQuery),
    ])
        .then((value) => {
            let resObj = {};
            value.forEach((v) => {
                resObj = Object.assign(resObj, v);
            });
            return resObj;
        })
        .catch(() => {
            return false;
        });

    return getFueskiData;
};

const handleUpdateRealTimeAll = async (req, res) => {
    if (!realtimeDb) {
        return res.status(400).send(ERROR_RESPONSE);
    }

    const fbOriginalData = await firebaseHelper.rtDbGet(realTimeDir);

    for (const config of subPlatformConfigs) {
        const { rtDbKey } = config;
        const subPlatformFbData = fbOriginalData?.[rtDbKey];
        if (!rtDbKey) {
            return;
        }

        for (const targetKey of Object.keys(realTimeKeys)) {
            const api = config[targetKey]?.api;
            const path = `${realTimeDir}/${rtDbKey}/${targetKey}`;
            if (
                api
                // query route
                // &&
                // Object.prototype.hasOwnProperty.call(
                //     subPlatformFbData,
                //     targetKey
                // ) // has key in real time
            ) {
                const resultData = await handleSparqlRead({
                    res,
                    api: config[targetKey]?.api,
                }).then(({ data }) =>
                    data?.reduce((acc, item) => {
                        if (
                            [realTimeKeys.hotBooks].includes(targetKey) &&
                            !acc[item.id]
                        ) {
                            const {
                                id,
                                date = "",
                                imgUrl = "",
                                bookZh = "",
                                bookEn = "",
                                langZh = "",
                                langEn = "",
                                authorsZh = "",
                                authorsEn = "",
                            } = item;
                            acc[id] = {
                                count: 0, // set default to 0
                                ...subPlatformFbData?.[targetKey]?.[id], // real time data (previous)
                                // update data
                                book: { zh: bookZh, en: bookEn },
                                date,
                                lang: { zh: langZh, en: langEn },
                                imgUrl,
                                authors: { zh: authorsZh, en: authorsEn },
                            };
                        } else if (
                            [realTimeKeys.hotPerson].includes(targetKey) &&
                            !acc[item.id]
                        ) {
                            const { id, labelZH = "", labelEN = "" } = item;
                            acc[id] = {
                                count: 0, // set default to 0
                                ...subPlatformFbData?.[targetKey]?.[id], // real time data (previous)
                                // update data
                                name: {
                                    zh: labelZH,
                                    en: labelEN,
                                },
                            };
                        }
                        return acc;
                    }, {})
                );

                // update Realtime database
                console.log("RealTime path: ", path);
                const rtUpdateRes = firebaseHelper.rtDbUpdate(path, resultData);

                if (rtUpdateRes instanceof Error) {
                    res.status(
                        errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.code
                    ).send(
                        errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.message
                    );
                    break;
                }
            }
        }
    }

    return res.status(200).send({
        updateTime: new Date().toISOString().replace("T", " ").split(".")[0],
        message: "Update Successfully!",
    });
};

module.exports = { handleUpdateRealTimeAll };
