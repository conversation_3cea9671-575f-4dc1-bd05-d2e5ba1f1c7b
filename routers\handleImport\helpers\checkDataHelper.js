const {
    doMakeApiQuery,
} = require("../../handleSparql/services/common/sparql-common");
const {
    replaceQueryParams,
} = require("../../handleSparql/services/common/common");
const { getApi } = require("../../handleAuth/apiObj");

const getListWithoutParams = (apiObj) => {
    const { path, ver } = apiObj;
    const query = getApi(path)[ver]?.query;
    return query ? doMakeApiQuery(query, -1, 0) : [];
};

const getListWithParams = (paramObj, apiObj) => {
    const { path, ver } = apiObj;
    const tmpQ = getApi(path)[ver]?.query;
    const query = replaceQueryParams(tmpQ, paramObj);
    return query ? doMakeApiQuery(query, -1, 0) : [];
};

const getLocIDObj = (rowData, key, geoColKey, locList) => {
    return locList.data.find(({ label, geoLatitude, geoLongitude }) => {
        return (
            label === rowData[key] &&
            geoLatitude === rowData[geoColKey[0]].toString() &&
            geoLongitude === rowData[geoColKey[1]].toString()
        );
    });
};

const checkWrongList = (allWrongList) => {
    return allWrongList.some((tmpArr) => tmpArr.length > 0);
};

/** 檢查欄位資料需要用到的清單、instance Id對照表 */
module.exports = {
    getListWithoutParams,
    getListWithParams,
    getLocIDObj,
    checkWrongList,
};
