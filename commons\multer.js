const multer = require("multer");
const fs = require("fs");
const path = require("path");
// const { getOs } = require("../commons/common");
const { UPLOAD_FIELD } = require("../config/config-upload");
const { getTargetPath, getFileNameFromPath } = require("./common");

const getYYYYMMDD = () => {
    const twoDigits = (num) => (Number(num) / 10 < 1 ? `0${num}` : num);
    const date = new Date();
    return `${date.getFullYear()}-${twoDigits(date.getMonth() + 1)}-${twoDigits(
        date.getDate()
    )}`;
};

const dateFileName = (fileName) => {
    return `${getYYYYMMDD()}_${fileName}`;
};

// 改成處理中的檔案名稱: {pattern}-{sheetName}-{timeStamp}.{ext}
const processFileName = (pattern, sheetName, fileName) => {
    return `${pattern}-${sheetName}-${Date.now()}.${fileName.split(".").pop()}`;
};

const getFileName = (pattern, sheetName, file) => {
    return pattern && sheetName
        ? processFileName(pattern, sheetName, file.originalname)
        : dateFileName(file.originalname);
};

const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const { type, sheetName } = req.query;
        const { pattern } = req.params;

        // let dir = "";
        // if (!(type && type in UPLOAD_FIELD[file.fieldname].dir)) {
        //     dir = UPLOAD_FIELD[file.fieldname].dir.default;
        // } else {
        //     dir = UPLOAD_FIELD[file.fieldname].dir[type];
        // }
        //
        // const targetPath =
        //     getOs() === "windows"
        //         ? path.join(process.env.FILE_SERVER_DST_WIN, dir || "")
        //         : path.join(process.env.FILE_SERVER_DST_LINUX, dir || "");
        const targetPath = getTargetPath(type, file.fieldname);

        const fileName = getFileName(pattern, sheetName, file);
        const filePath = path.join(targetPath, fileName);

        // 儲存路徑, 以便後面的 middleware 使用
        req.targetPath = [...(req.targetPath || []), filePath];
        // mkdir
        fs.mkdirSync(targetPath, { recursive: true }, () => {});
        cb(null, targetPath);
    },
    filename: (req, file, cb) => {
        const { targetPath } = req;
        targetPath.forEach((pathStr) => {
            const fileName = getFileNameFromPath(pathStr);
            cb(null, fileName);
        });
    },
});

const getExtReg = (type) => {
    let extensions, mineTypePrefix;
    if (type in UPLOAD_FIELD) {
        extensions = UPLOAD_FIELD[type].ACCEPTABLE_EXTENSIONS;
        mineTypePrefix = UPLOAD_FIELD[type].mineTypePrefix;
    }

    switch (type) {
        // case UPLOAD_FIELD.image.name:
        //     return `^${mineTypePrefix}\\/(${extensions.join("|")})$`;
        case UPLOAD_FIELD.file.name:
            return `^${mineTypePrefix}\\/(${extensions.join("|")})$`;
        default:
            return null;
    }
};

module.exports = (fieldName) =>
    multer({
        storage,
        fileFilter: (req, file, cb) => {
            let extReg = getExtReg(fieldName);

            if (!extReg) {
                req.failureCount =
                    req.failureCount != null ? req.failureCount + 1 : 1;
                cb(null, false);
            }

            if (file.mimetype.match(extReg)) {
                req.fileNames = [...(req.fileNames || []), file.originalname];
                req.fileValidation = [...(req.fileValidation || []), true];
                // req.fileValidation = true;
                // 統計符合的檔案個數: 使用在 response
                req.successCount =
                    req.successCount != null ? req.successCount + 1 : 1;
                req.failureCount =
                    req.failureCount != null ? req.failureCount : 0;

                cb(null, true);
            } else {
                req.failureCount =
                    req.failureCount != null ? req.failureCount + 1 : 1;
                // req.fileValidation = false;
                req.fileValidation = [...(req.fileValidation || []), false];
                cb(null, false);
            }
        },
        limits: {
            fileSize: UPLOAD_FIELD[fieldName].maxSize,
            files: UPLOAD_FIELD[fieldName].maxCount,
        },
    });
