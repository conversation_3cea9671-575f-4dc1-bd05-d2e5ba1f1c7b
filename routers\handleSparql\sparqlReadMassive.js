const { verifyToken } = require("../handleAuth/auth");
const { getApi } = require("../handleAuth/apiObj");
const {
    DEFAULT_MAX_LIMIT,
    DEFAULT_OFFSET,
    RESPONSE_OK,
    RESPONSE_BAD_REQUEST,
    REDUNDANT_PREFIX,
    ERROR_WRONG_PARAMS,
    ERROR_NO_PARAM_VER,
    ERROR_API_METHOD,
    ERROR_API_NOT_EXIST,
    ERROR_API_VER_NOT_EXIST,
    ERROR_LACK_OF_PARAMS,
} = require("../../config/config");
const {
    replaceQueryParams,
    map2SparqlLang,
} = require("./services/common/common");
const {
    doMakeApiQuery,
    doMakeCountQuery,
} = require("./services/common/sparql-common");

async function sparqlReadMassive(req, res, next) {
    if (!req.body.hasOwnProperty("entry")) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_NO_PARAM_VER);
    }
    const authToken = req.headers.authorization;
    const { entry } = req.body;

    // The API has to have the version.
    const { ver, lang } = req.params;
    if (!ver) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_NO_PARAM_VER);
    }
    const language = map2SparqlLang(lang);

    // The API method is not correct.
    let apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_API_METHOD);
    }

    // API method with redundant prefix "_".
    // ex: _ra/nmtl/footer/1.0?limit=-1&offset=0
    //     _ra is redundant, has to remove it.
    if (apiMethod[0] === REDUNDANT_PREFIX) {
        apiMethod = apiMethod.substring(apiMethod.indexOf("/") + 1);
    }
    // console.log(apiMethod, ver, language);

    // The API method doesn't exist.
    const apiDef = getApi(apiMethod);
    if (!apiDef) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_API_NOT_EXIST);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    // The parameters for API are available.
    const methodParams = apiVer.params;
    if (Object.keys(req.query).length !== methodParams.length) {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_WRONG_PARAMS);
    }

    // console.log("methodParams::", methodParams);
    const foundParams = methodParams.filter((param) => {
        if (!req.query.hasOwnProperty(param)) {
            return true;
        }
    });

    // console.log("foundParams::", foundParams);
    if (foundParams.length > 0 && foundParams) {
        const lackParams = foundParams.join(", ");
        return res
            .status(RESPONSE_BAD_REQUEST)
            .send({ error: `${ERROR_LACK_OF_PARAMS.error} ${lackParams}` });
    }

    // limit and offset
    const limit = req.query.hasOwnProperty("limit")
        ? req.query.limit
        : DEFAULT_MAX_LIMIT;
    const offset = req.query.hasOwnProperty("offset")
        ? req.query.offset
        : DEFAULT_OFFSET;

    // req.query:: { limit: '-1', offset: '0', ds: 'tltc', type: 'URLEvent', ids: '' }
    // 有些值太大，藏在 body 裡
    Object.keys(req.query).forEach((key) => {
        if (req.query[key].length === 0 && entry.hasOwnProperty(key)) {
            // 這個 key 在 body 裡有，而且傳進來的值為空值，取代
            req.query[key] = entry[key];
        }
    });

    // The query string.
    let apiQuery = replaceQueryParams(
        apiVer.query,
        req.query,
        apiVer.key,
        language
    );
    if (apiQuery === "") {
        return res.status(RESPONSE_BAD_REQUEST).send(ERROR_WRONG_PARAMS);
    }

    // console.log("sparqlReadMassive::apiQuery::", apiQuery);
    await verifyToken(authToken)
        .then(async () => {
            await Promise.all([
                doMakeApiQuery(apiQuery, limit, offset),
                doMakeCountQuery(apiQuery),
            ])
                .then((value) => {
                    // console.log(value);
                    let resObj = {};
                    value.forEach((v) => {
                        resObj = Object.assign(resObj, v);
                    });
                    req.daoyiData = resObj;
                    req.daoyiStatus = RESPONSE_OK;
                    next();
                })
                .catch((err) => {
                    req.daoyiData = err.message;
                    req.daoyiStatus = RESPONSE_BAD_REQUEST;
                    next();
                });
        })
        .catch((error) => {
            console.log(error);
            req.daoyiData = error;
            req.daoyiStatus = RESPONSE_BAD_REQUEST;
            next();
        });
}

module.exports = sparqlReadMassive;
