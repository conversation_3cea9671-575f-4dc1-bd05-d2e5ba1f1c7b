const axios = require("axios");
const exportConfig = require("../../../config/config.export");
const {
    getSheetObj,
    getSheetHeaderId,
    isGetRangeLabel,
} = require("../../handleAuth/apiObj");
const timerHelper = require("./timerHelper");
const commonHelper = require("./commonHelper");

const dbHelper = {};

const getData = async (url) => {
    try {
        const response = await axios.get(url);
        return response.data["data"];
    } catch (error) {
        return [];
    }
};

const getAllIds = async (url) => {
    const idList = [];
    const res = await getData(url);
    res.forEach((r) => {
        const { id, label } = r;
        if (!id || !label) {
            return;
        }
        idList.push(id);
    });
    // console.log(idList);
    return idList;
};

const getPostData = async (url, ids) => {
    try {
        return await axios({
            method: "post",
            url,
            data: {
                entry: { ids },
            },
        }).then((response) => {
            return response.data.data;
        });
    } catch (error) {
        return [];
    }
};

const getIdValues = async (url, ids, headers) => {
    const promises = [];
    const maxReadIds = exportConfig.MAX_POST_IDS;
    // 慢慢抓比較快
    const queryTimes = Math.ceil(ids.length / maxReadIds);
    // console.log("Do the Math::", ids.length, maxReadIds, queryTimes);
    for (let t = 0; t < queryTimes; t = t + 1) {
        const idsStr = ids
            .slice(t * maxReadIds, (t + 1) * maxReadIds)
            .join(",");
        // console.log(idsStr, t * splitNum, (t + 1) * splitNum);
        const queryPromise = new Promise((resolve, reject) => {
            resolve(getPostData(url, idsStr));
        });
        promises.push(queryPromise);
    }

    // 提昇速度
    const headerObj = headers.reduce((a, v) => ({ ...a, [v]: 1 }), {});

    let mergedData = {};
    await Promise.all(promises)
        .then((values) => {
            values.forEach((arr) => {
                for (const itm of arr) {
                    const { srcId, p, o } = itm;

                    if (!(p in headerObj)) {
                        continue;
                    }

                    if (!(srcId in mergedData)) {
                        mergedData[srcId] = { [p]: [o] };
                    } else {
                        if (p in mergedData[srcId]) {
                            if (!(o in mergedData[srcId][p])) {
                                mergedData[srcId][p].push(o);
                            }
                            // FIXME:: 確認這裡是否需要填入""
                            else {
                                mergedData[srcId][p].push("");
                            }
                        } else {
                            mergedData[srcId][p] = [o];
                        }
                    }
                }
            });
        })
        .catch((error) => {
            console.error(error.message);
        });

    // console.log("Out Promise::", mergedData);
    return mergedData;
};

const getSubValues = async (mData, graph) => {
    const maxReadIds = exportConfig.MAX_POST_IDS;

    // id 轉 label
    const needToGetLabelObj = {};
    // subValues 轉 property
    const needToGetSubValObj = {};

    // 每個 column 取一次
    for (const sId in mData) {
        for (const sp in mData[sId]) {
            // 從 property 判斷要不要抓 label 或 subValue
            const ranges = isGetRangeLabel(sp);
            // console.log("sp::", sp, ranges);
            if (!ranges || ranges.length === 0) {
                // data
                continue;
            }

            if (
                commonHelper.isArrayElementInArray(
                    exportConfig.DATE_EVENT,
                    ranges
                )
            ) {
                // 把 DateEvent 的 prefix 清掉
                for (const date in mData[sId][sp]) {
                    // 0000-00-00 不要顯示
                    mData[sId][sp][date] = mData[sId][sp][date]
                        .replace(exportConfig.DATE_EVENT_PREFIX, "")
                        .replace("0000-00-00", "");
                }
            } else if (sp in exportConfig.SubValues) {
                // 特殊取法，必須與後台一致!
                for (const subId of mData[sId][sp]) {
                    if (!(subId in needToGetSubValObj)) {
                        needToGetSubValObj[subId] = 1;
                    }
                }
            } else {
                // 把需要取 label 的所有 instances 收集起來，後面再取值
                for (const subId of mData[sId][sp]) {
                    if (!(subId in needToGetLabelObj)) {
                        needToGetLabelObj[subId] = 1;
                    }
                }
            }
        }
    }

    const needToGetLabel = Object.keys(needToGetLabelObj);
    const needToGetSubVal = Object.keys(needToGetSubValObj);
    // ----------------------------------------------------
    // 取 id 的 label 對應
    // console.log("needToGetLabel::", needToGetLabel.length);
    if (needToGetLabel.length > 0) {
        const promises = [];
        // 抓 subId 的 label
        const queryTimes = Math.ceil(needToGetLabel.length / maxReadIds);
        // console.log("Do the Math::", ids.length, maxReadIds, queryTimes);
        for (let t = 0; t < queryTimes; t = t + 1) {
            const idsStr = needToGetLabel
                .slice(t * maxReadIds, (t + 1) * maxReadIds)
                .join(",");
            // console.log(idsStr, t * splitNum, (t + 1) * splitNum);
            const postUrl = exportConfig.getLabel;
            // `https://api2.daoyidh.com/nmtl2/zh-tw/POST/required/label/1.0?limit=-1&offset=0&ids=`;

            const queryPromise = new Promise((resolve, reject) => {
                resolve(getPostData(postUrl, idsStr));
            });
            promises.push(queryPromise);
        }

        await Promise.all(promises)
            .then((values) => {
                // 先變成 id -> label 對應表，比較快
                const idMapLabel = {};
                values.forEach((arr) => {
                    // console.log("subValues id label::", arr);
                    for (const itm of arr) {
                        const { id, label } = itm;
                        if (!(id in idMapLabel)) {
                            idMapLabel[id] = label;
                        }
                    }
                });

                // console.log("idMapLabel::", idMapLabel);
                // 開始對應
                // FIXME
                const noLabelIdObj = {};
                for (const sId in mData) {
                    for (const sp in mData[sId]) {
                        for (const sValIdx in mData[sId][sp]) {
                            const sVal = mData[sId][sp][sValIdx];
                            if (sVal in idMapLabel) {
                                mData[sId][sp][sValIdx] = idMapLabel[sVal];
                            } else if (sVal in needToGetLabelObj) {
                                // FIXME: 有 id，但是沒有 label，需要查查!!
                                mData[sId][sp][sValIdx] = "";

                                // FIXME
                                if (!(sVal in noLabelIdObj)) {
                                    noLabelIdObj[sVal] = 1;
                                }
                            }
                        }
                    }
                }

                // FIXME
                const noLabelId = Object.keys(noLabelIdObj);
                if (noLabelId.length > 0) {
                    console.log("Error!! no label Ids::", noLabelId);
                }
            })
            .catch((error) => {
                console.error(error.message);
            });
    }

    // ----------------------------------------------------
    // console.log("needToGetSubVal::", needToGetSubVal.length);
    // 特殊取法，必須與後台一致!
    if (needToGetSubVal.length > 0) {
        const promises = [];
        // 抓 subId 的 label
        const queryTimes = Math.ceil(needToGetSubVal.length / maxReadIds);
        // console.log("Do the Math::", ids.length, maxReadIds, queryTimes);
        for (let t = 0; t < queryTimes; t = t + 1) {
            const idsStr = needToGetSubVal
                .slice(t * maxReadIds, (t + 1) * maxReadIds)
                .join(",");
            // console.log(idsStr, t * splitNum, (t + 1) * splitNum);
            const postUrl = exportConfig.getSubValSpo(graph);
            // `https://api2.daoyidh.com/nmtl2/zh-tw/POST/required/subvalue/1.0?limit=-1&offset=0&ds=${graph}&ids=`;

            const queryPromise = new Promise((resolve, reject) => {
                resolve(getPostData(postUrl, idsStr));
            });
            promises.push(queryPromise);
        }

        await Promise.all(promises)
            .then((values) => {
                // console.log("needToGetSubVal::", values);
                // 先變成 id -> label 對應表，比較快
                const spoMap = {};
                values.forEach((arr) => {
                    // console.log("subValues id label::", arr);
                    for (const itm of arr) {
                        const { srcId, p, o } = itm;
                        if (!(srcId in spoMap)) {
                            spoMap[srcId] = {};
                        }

                        if (!(p in spoMap[srcId])) {
                            spoMap[srcId][p] = [o];
                        } else {
                            spoMap[srcId][p].push(o);
                        }
                    }
                });

                // console.log("idMapLabel::", spoMap);
                // 重組 property 變成跟後台表格一樣
                // ex: imagePath, imageName => imageURL_hasURL
                for (const srcIdTmp in spoMap) {
                    const { type } = spoMap[srcIdTmp];
                    // subKey:
                    // {
                    //     type: 'URLEvent',
                    //     property: [ 'imageName', 'imagePath', 'imageFrom', 'hasCopyrightStatus' ],
                    //     key: 'hasURL'
                    // }
                    const subKey = commonHelper.getSubValueType(type);
                    // console.log("subKey::", subKey);
                    for (const sp of subKey.property) {
                        if (sp === exportConfig.IMAGE_PATH) {
                            // 跟 imageName 一起處理
                            continue;
                        }
                        // label 特殊處理
                        if (
                            sp === exportConfig.LABEL &&
                            sp in spoMap[srcIdTmp]
                        ) {
                            const newSp = `${exportConfig.LABEL}_${type}`;
                            spoMap[srcIdTmp][newSp] = spoMap[srcIdTmp][sp];

                            delete spoMap[srcIdTmp][sp];
                            continue;
                        }
                        if (
                            sp === exportConfig.IMAGE_NAME &&
                            sp in spoMap[srcIdTmp]
                        ) {
                            const imageName = spoMap[srcIdTmp][sp];
                            const imagePath =
                                spoMap[srcIdTmp][exportConfig.IMAGE_PATH];
                            const newSp = `${exportConfig.IMAGE_URL}_${subKey.key}`;
                            spoMap[srcIdTmp][newSp] = [
                                `${imagePath}/${imageName}`,
                            ];

                            delete spoMap[srcIdTmp][sp];
                            delete spoMap[srcIdTmp][exportConfig.IMAGE_PATH];
                            continue;
                        }

                        // others
                        const newOtherSp = `${sp}_${subKey.key}`;
                        spoMap[srcIdTmp][newOtherSp] = spoMap[srcIdTmp][sp] || [
                            "",
                        ];
                        delete spoMap[srcIdTmp][sp];
                    }
                    // console.log("type, subKey::", type, subKey);
                }
                // console.log("spoMap::", spoMap);

                // 開始對應
                for (const sId in mData) {
                    for (const sp in mData[sId]) {
                        // 值對應
                        for (const sEvtId of mData[sId][sp]) {
                            // sEvtId: URLEVT872, SOU42421
                            if (sEvtId in spoMap) {
                                for (const spoP of Object.keys(
                                    spoMap[sEvtId]
                                )) {
                                    // 可能有多個
                                    if (spoP in mData[sId]) {
                                        mData[sId][spoP].concat(
                                            spoMap[sEvtId][spoP]
                                        );
                                    } else {
                                        mData[sId][spoP] = spoMap[sEvtId][spoP];
                                    }
                                }
                            }
                        }
                    }
                }
                // console.log("mData::", mData);
            })
            .catch((error) => {
                console.error(error.message);
            });
    }

    // 為了 xlsx 輸出，要把 value 的 array 變成 string
    for (const sId in mData) {
        for (const sp in mData[sId]) {
            mData[sId][sp] = mData[sId][sp].join("\n");
        }
    }
    return mData;
};

dbHelper.databaseExport = async (file) => {
    try {
        const { graph, sheet } = file || {};

        const startTime = new Date();
        timerHelper.timeStart("databaseExport", startTime);

        const sheetObj = getSheetObj();
        const outputData = [];

        for (const g of graph) {
            for (const sh of sheet) {
                if (sh in sheetObj) {
                    const { getClassList, getTable2 } = sheetObj[sh];
                    const url = exportConfig.getAllIdPath(getClassList, g);
                    // `https://api2.daoyidh.com/nmtl2/zh-tw/${getClassList}?limit=-1&offset=0&ds=${g}`;

                    // 先取出表格所有的 id
                    const allIds = await getAllIds(url);
                    const postUrl = exportConfig.getSpoPath(getTable2, g);
                    // `https://api2.daoyidh.com/nmtl2/zh-tw/POST/${getTable2}?limit=-1&offset=0&ds=${g}&ids=`;

                    // 針對 id 取出所有的 subValue
                    // headers 要加入特殊的取值法，如：hasURL, hasSource...
                    const headers = getSheetHeaderId(sh).concat(
                        commonHelper.getSubValueProperty()
                    );
                    // console.log(headers);
                    const mergedData = await getIdValues(
                        postUrl,
                        allIds,
                        headers
                    );

                    // 取出的 subValue 還需要取它的 label 或 subValue
                    const mergedSubData = await getSubValues(mergedData, graph);

                    const xlsxData = [];
                    for (const vId in mergedSubData) {
                        const row = Object.assign(mergedSubData[vId], {
                            srcId: vId,
                        });
                        xlsxData.push(row);
                    }
                    outputData.push({
                        head: headers,
                        data: xlsxData,
                        graph: g,
                        sheet: sh,
                    });
                }
            }
        }

        timerHelper.timeStampEnd("databaseExport", startTime);
        return outputData;
    } catch (err) {
        console.log(err);
        return { error: err.message };
    }
};

module.exports = dbHelper;
