const { personObj } = require("../../../sparql/properties/myProperty");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const deletePerson = async (entry) => {
    const personIri = personObj(entry);
    //
    const _queryStr = `
        DELETE {
            GRAPH ?g {
                ${personIri} ?P1 ?O1 .
                ?S2 ?P2 ${personIri} .
            }
        }
        WHERE {
            GRAPH ?g { 
                { 
                    ${personIri} ?P1 ?O1 . 
                }
                UNION
                { 
                    ?S2 ?P2 ${personIri} . 
                }
            }
        }
    `;

    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.deletePerson20 = async (entry, callback) => {
    await Promise.all([deletePerson(entry)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
