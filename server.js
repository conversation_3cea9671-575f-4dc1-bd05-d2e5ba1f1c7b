const express = require("express");
const bodyParser = require("body-parser");
const app = express();
const safeHeader = require("./routers/handleHeader");
const useEnv = require("./commons/useEnv");

// use .env.development or .env.production
useEnv();

app.use(bodyParser.json({ limit: "50mb", extended: true }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(safeHeader);

require("./routers/routerManager.js")(app);

const PORT = process.env.PORT || 3010;
const server = app.listen(PORT, "0.0.0.0", () => {
    console.log(`Listen to port ${PORT}`);
    console.log("[ENV] NODE_ENV: ", process.env.NODE_ENV || "development");
});
