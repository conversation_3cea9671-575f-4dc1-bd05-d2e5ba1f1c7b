const namespace = {
    graph: "http://www.nmtl.gov.tw/v1/",
    base: "http://www.nmtl.gov.tw/v1#",
    rdf: "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
    rdfs: "http://www.w3.org/2000/01/rdf-schema#",
    xsd: "http://www.w3.org/2001/XMLSchema#",
    owl: "http://www.w3.org/2002/07/owl#",
    text: "http://jena.apache.org/text#",
    skos: "http://www.w3.org/2004/02/skos/core#",
};
const iriPrefix = "nmtl";
const MAX_INS_DEFINE = "MAX_INS";
const MAX_INS_PREFIX = "max_";
const MAX_VALUE_GRAPH = "settings";

const removePrefix = (iri) => {
    const found = Object.keys(namespace).find((key) => {
        const prefix = namespace[key];
        return iri.startsWith(prefix);
    });
    return iri.replace(new RegExp(namespace[found], "g"), "");
};

const removeIri = (iri) => {
    const found = Object.keys(namespace).find((key) => {
        const prefix = namespace[key];
        return iri.includes(prefix);
    });
    // <xxxxxx>
    return iri.replace(new RegExp(namespace[found], "g"), "").slice(1, -1);
};

module.exports = {
    namespace,
    removeBindingsPrefix(bindings) {
        return bindings.map((b) => {
            // #FIXME#, 取代全臺詩的特殊符號：`[$?$]`
            const wordRegex = /(`\[\$(\d)\$\]`)/gi;
            // #FIXME#, 移除全臺詩的特殊符號：`[*?*]`, `[%?%]`
            const twpRegex = /(`\[([^\]]+)\]`)/gi;
            const newBind = {};
            Object.keys(b).forEach((key) => {
                // newBind[key] = removePrefix(b[key].value);
                newBind[key] = removePrefix(b[key].value)
                    .replace(
                        wordRegex,
                        "![word_$2](https://fs.daoyidh.com/static/words/word_$2.png)"
                    )
                    .replace(twpRegex, "");
            });
            return newBind;
        });
    },
    removeValueIri(value) {
        return removeIri(value);
    },
    graphIri(value) {
        return `<${namespace.graph}${value}>`;
    },
    baseIri(value) {
        return `<${namespace.base}${value}>`;
    },
    rdfsIri(value) {
        return `<${namespace.rdfs}${value}>`;
    },
    maxInsIri() {
        return `<${namespace.base}${MAX_INS_DEFINE}>`;
    },
    maxValueIri(prefix) {
        return `<${namespace.base}${MAX_INS_PREFIX}${prefix}>`;
    },
    maxValueGraph() {
        return `<${namespace.graph}${MAX_VALUE_GRAPH}>`;
    },
    prjIri() {
        return iriPrefix;
    },
    prefixesIri() {
        return `
PREFIX ${iriPrefix}: <${namespace.base}>
PREFIX rdf: <${namespace.rdf}>
PREFIX rdfs: <${namespace.rdfs}>
PREFIX xsd: <${namespace.xsd}>
PREFIX owl: <${namespace.owl}>
PREFIX text: <${namespace.text}>
PREFIX skos: <${namespace.skos}>`;
    },
};
