const { baseIri, graphIri } = require("../rdf");
const {
    isObjectEqual,
    replIllegalIRIChar,
    getStrokeLanguageTag,
    insertMultipleLabel,
} = require("../../common/common");
const {
    isRandomId,
    isEventType,
    isSupportType,
    getTypeFromPrefix,
    getStroke,
} = require("./common");
const { processSubValue } = require("./subValues");
const { myProperty } = require("./myProperty");
const { dummyType } = require("./dummyType");
const { NameStrokeClass } = require("../classPrefix");
const defines = require("./bindIdDefine");

const baseUri = defines.baseUri;

// 定義特殊 property 以創建 instances
const _CreateInstance = defines._CreateInstance;
// 資料放在 _Value 裡
const _Value = defines._Value;
const _ValueSTARTS = defines._ValueSTARTS;

exports.createObj = (entry) => {
    const resObj = { insert: "", remove: "", where: "", insertGraph: "" };
    const { srcId: label, classType } = entry;
    // 控制有些值在 create 時能新增，delete 時不用刪除
    const isAdd = true;
    let bindId;

    // property 及其值，放在 value 裡
    if (!entry.hasOwnProperty(_Value)) {
        console.log("ERROR::", _Value, " is required!");
        return resObj;
    }

    // 只處理 Dummy 的 SubValue，如：新舊組織
    if (classType === defines._DUMMY) {
        const { dummyProcessed, dummyWhere } = dummyType(
            classType,
            entry.value,
            isAdd
        );
        resObj.where += dummyWhere;
        resObj.insert += dummyProcessed;
        return resObj;
    }

    const foundLabel = Object.keys(entry[_Value]).find((key) =>
        key
            .toLowerCase()
            .startsWith(`${_ValueSTARTS}${classType}`.toLowerCase())
    );

    if (entry[_Value].hasOwnProperty(_CreateInstance) || foundLabel) {
        // 取出 label 的值
        const createInsLabel = entry[_Value][foundLabel || _CreateInstance];

        // 如果選擇已存在的 instance，則 srcId 的 prefix 符合 classType 的定義
        const foundPrefix = getTypeFromPrefix(label);
        if (
            foundPrefix &&
            foundPrefix.eventType.toLowerCase() === classType.toLowerCase()
        ) {
            bindId = `${defines._bindAddId}_${classType}`;

            // 產生完整的 iri
            const srcId = baseIri(replIllegalIRIChar(label));

            resObj.insert += `?${bindId} a ${baseIri(foundPrefix.eventType)} .`;
            // Bug Fixed: label_Person: ["test@zh", "testen@en"], 會新增一個 label "test@zh,testen@en"
            resObj.insert += insertMultipleLabel(createInsLabel);

            resObj.insert += myProperty(entry, `?${bindId}`, isAdd);
            // 自動產生 lastModified
            resObj.insert += `?${bindId} ${
                defines._LastModified
            } '''${new Date().getTime()}''' .`;
            resObj.where += `BIND(IRI(${srcId}) AS ?${bindId}) .`;

            // 自動產生 nameStroke
            if (NameStrokeClass.indexOf(foundPrefix.eventType) > -1) {
                const [pureLabel] = getStrokeLanguageTag(createInsLabel);
                resObj.insertGraph += `GRAPH ${graphIri("stroke")} {
                    ?${bindId} ${defines._NameStroke} '''${getStroke(
    pureLabel
)}'''^^xsd:integer .}`;
            }

            // sub values
            const { subWhere, processed } = processSubValue(
                bindId,
                classType,
                entry.value
            );
            resObj.where += subWhere;
            resObj.insert += processed;
            return resObj;
        }

        // 利用 BNODE() 隨機產生一個不重複的 id
        const randObj = isRandomId(classType);

        // 創建新的 instance
        if (randObj) {
            bindId = `${defines._NewRandomId}_${classType}`;

            // 1. instance 為 random id
            resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnode_${classType}) .`;
            resObj.where += `BIND(IRI(CONCAT("${baseUri}", "${randObj.prefix}_", ?bnode_${classType})) AS ?${bindId}) .`;
            resObj.insert += `?${bindId} a ${baseIri(randObj.eventType)} .`;

            // Bug Fixed: label_Person: ["test@zh", "testen@en"], 會新增一個 label "test@zh,testen@en"
            resObj.insert += insertMultipleLabel(createInsLabel);

            // 自動產生 lastModified
            resObj.insert += `?${bindId} ${
                defines._LastModified
            } '''${new Date().getTime()}''' .`;

            // 自動產生 nameStroke
            if (NameStrokeClass.indexOf(foundPrefix.eventType) > -1) {
                const [pureLabel] = getStrokeLanguageTag(createInsLabel);
                resObj.insertGraph += `GRAPH ${graphIri("stroke")} {
                    ?${bindId} ${defines._NameStroke} '''${getStroke(
    pureLabel
)}'''^^xsd:integer .}`;
            }

            resObj.insert += myProperty(entry, `?${bindId}`, isAdd);
        } else {
            bindId = `${defines._bindAddId}_${classType}`;

            // 2. instance 與 label 相關
            const supportType = isSupportType(classType);
            if (supportType) {
                const srcId = baseIri(
                    `${supportType.prefix}${replIllegalIRIChar(createInsLabel)}`
                );
                resObj.where += `BIND(IRI(${srcId}) AS ?${bindId}) .`;
                resObj.insert += `?${bindId} a ${baseIri(
                    supportType.eventType
                )} .`;

                // Bug Fixed: label_Person: ["test@zh", "testen@en"], 會新增一個 label "test@zh,testen@en"
                resObj.insert += insertMultipleLabel(createInsLabel);

                // 自動產生 lastModified
                resObj.insert += `?${bindId} ${
                    defines._LastModified
                } '''${new Date().getTime()}''' .`;

                // 自動產生 nameStroke
                if (NameStrokeClass.indexOf(foundPrefix.eventType) > -1) {
                    const [pureLabel] = getStrokeLanguageTag(createInsLabel);
                    resObj.insertGraph += `GRAPH ${graphIri("stroke")} {
                    ?${bindId} ${defines._NameStroke} '''${getStroke(
    pureLabel
)}'''^^xsd:integer .}`;
                }

                resObj.insert += myProperty(entry, `?${bindId}`, isAdd);
            } else {
                console.error(classType, " is not supported!", entry);
                return resObj;
            }
        }
    } else {
        // Event 類別
        const typeObj = isEventType(classType);

        if (typeObj) {
            bindId = `${defines._NewRandomId}_${classType}`;

            resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnode_${classType}) .`;
            resObj.where += `BIND(IRI(CONCAT("${baseUri}", "${typeObj.prefix}_", ?bnode_${classType})) AS ?${bindId}) .`;
            // 自動產生 lastModified
            resObj.insert += `?${bindId} ${
                defines._LastModified
            } '''${new Date().getTime()}''' .`;

            resObj.insert += myProperty(entry, `?${bindId}`, isAdd);
        } else {
            // label 不能為空
            if (label === "") {
                return resObj;
            }

            bindId = `${defines._bindAddId}_${classType}`;

            const srcId = baseIri(replIllegalIRIChar(label));
            resObj.where += `BIND(IRI(${srcId}) AS ?${bindId}) .`;
            // 自動產生 lastModified
            resObj.insert += `?${bindId} ${
                defines._LastModified
            } '''${new Date().getTime()}''' .`;
            resObj.insert += myProperty(entry, `?${bindId}`, isAdd);

            // 新增的 instance 如果已經有 label，在被新增的 graph 必須定義 class 才能被顯示。
            resObj.insert += `?${bindId} a ${baseIri(classType)} .`;
        }
    }

    // sub values
    const { subWhere, processed } = processSubValue(
        bindId,
        classType,
        entry.value
    );
    resObj.where += subWhere;
    resObj.insert += processed;

    return resObj;
};

exports.deleteObj = (entry) => {
    const resObj = { insert: "", remove: "", where: "" };
    const { srcId: tmpSrcId } = entry;

    if (!tmpSrcId || tmpSrcId === "") {
        return resObj;
    }
    const srcId = baseIri(replIllegalIRIChar(tmpSrcId));

    resObj.remove += `?s ?p ?o .`;
    resObj.where += `
        {
            BIND(${srcId} AS ?s) .
            ?s ?p ?o .
        }
        UNION
        {
            BIND(${srcId} AS ?o) .
            ?s ?p ?o .
        }`;

    return resObj;
};

exports.updateObj = (entrySrc, entryDst) => {
    const resObj = {
        insert: "",
        where: "",
        remove: "",
        insertGraph: "",
        removeGraph: "",
    };
    // 以 entrySrc 的 srcId, classType 為主
    const { srcId, classType } = entrySrc;

    // 只處理 SubValue，如：新舊組織
    if (classType === defines._DUMMY) {
        // entrySrc
        const { dummyProcessed: srcDummyProcessed } = dummyType(
            classType,
            entrySrc.value,
            false
        );
        resObj.remove += srcDummyProcessed;

        // entryDst
        const { dummyProcessed: dstDummyProcessed, dummyWhere: dstDummyWhere } =
            dummyType(classType, entryDst.value, true);
        resObj.insert += dstDummyProcessed;
        resObj.where += dstDummyWhere;
        return resObj;
    }

    // Update 必定會有 srcId
    if (!srcId || srcId === "") {
        return resObj;
    }

    const iriSrcId = baseIri(replIllegalIRIChar(srcId));

    entryDst.srcId = srcId;
    entryDst.classType = classType;

    // no change
    if (isObjectEqual(entrySrc, entryDst)) {
        return resObj;
    }

    let bindId = `${defines._bindUpdId}_${classType}`;
    // Event 類型的資料, 使用 _NewRandomId
    const typeObj = isEventType(classType);

    if (typeObj) {
        bindId = `${defines._NewRandomId}_${typeObj.eventType}`;
    }

    resObj.where += `BIND(IRI(${iriSrcId}) AS ?${bindId}) .`;
    // 自動更新lastModified
    resObj.where += `OPTIONAL {?${bindId} ${defines._LastModified} ?lastModified .}`;
    // delete old
    resObj.remove += myProperty(entrySrc, `?${bindId}`, false);
    // 自動更新lastModified
    resObj.remove += `?${bindId} ${defines._LastModified} ?lastModified .`;
    // create new
    resObj.insert += myProperty(entryDst, `?${bindId}`, true);
    // 自動更新lastModified
    resObj.insert += `?${bindId} ${
        defines._LastModified
    } '''${new Date().getTime()}''' .`;

    // 自動產生 nameStroke
    if (NameStrokeClass.indexOf(classType) > -1) {
        const foundLabel = Object.keys(entryDst[_Value]).find((key) =>
            key
                .toLowerCase()
                .startsWith(`${_ValueSTARTS}${classType}`.toLowerCase())
        );

        if (foundLabel) {
            // 取出 label 的值
            let createInsLabel =
                entryDst[_Value][foundLabel || _CreateInstance];
            const [pureLabel] = getStrokeLanguageTag(createInsLabel);

            resObj.where += `OPTIONAL {?${bindId} ${defines._NameStroke} ?nameStroke .}`;
            resObj.removeGraph += `GRAPH ${graphIri("stroke")} {
            ?${bindId} ${defines._NameStroke} ?nameStroke .}`;
            resObj.insertGraph += `GRAPH ${graphIri("stroke")} {
            ?${bindId} ${defines._NameStroke} '''${getStroke(
    pureLabel
)}'''^^xsd:integer .}`;
        }
    }

    // ---- Sub Values start ----
    // sub values: entrySrc
    const { subWhere: srcWhere, processed: srcRemove } = processSubValue(
        bindId,
        classType,
        entrySrc.value
    );
    const srcWhereStr = srcWhere;
    resObj.remove += srcRemove;

    // sub values: entryDst
    const {
        subWhere: dstWhere,
        processed: dstInsert,
        subRemove: dstRemove,
    } = processSubValue(bindId, classType, entryDst.value);
    // 如果情形為刪除字串，會造成 dstEntry 沒有值，只能以 srcEntry 的 id 來使用。
    resObj.where += dstWhere === "" ? srcWhereStr : dstWhere;
    resObj.insert += dstInsert;
    resObj.remove += dstRemove;
    // ---- Sub Values end ----

    return resObj;
};
