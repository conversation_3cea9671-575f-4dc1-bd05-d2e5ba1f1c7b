const { verifyToken } = require("../handleAuth/auth");
const {
    ERROR_NO_PARAM_VER,
    ERROR_LACK_OF_PARAMS,
    ERROR_RESPONSE,
} = require("../../config/config");
const mongoQuery = require("../handleSparql/services/mongodb/mongodbAPI");

function mongodbReadGeneral(req, res) {
    const authToken = req.headers.authorization;
    const { ver } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }

    const { type } = req.query;
    if (!type) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    const apiQuery = { type };
    verifyToken(authToken)
        .then(() => {
            mongoQuery.makeQuery(
                apiQuery,
                { _id: 0, data: 1 },
                (response, error) => {
                    if (error) {
                        return res.status(400).send({ error });
                    } else {
                        if (
                            response.length < 1 ||
                            !response[0].hasOwnProperty("data")
                        ) {
                            return res.status(400).send(ERROR_RESPONSE);
                        }
                        const data = response[0].data;
                        if (Array.isArray(data)) {
                            data.sort((a, b) => {
                                let valA = 0;
                                let valB = 0;
                                if (a.hasOwnProperty("id")) {
                                    valA = a["id"];
                                }
                                if (b.hasOwnProperty("id")) {
                                    valB = b["id"];
                                }
                                return valA - valB;
                            });
                        }
                        res.status(200).send({ data });
                    }
                }
            );
        })
        .catch((error) => {
            res.status(400).send({ error });
        });
}

module.exports = mongodbReadGeneral;
