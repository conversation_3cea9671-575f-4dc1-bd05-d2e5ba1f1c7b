const firebase = require("../../config/config.firebase");
const { RESPONSE_OK } = require("../../config/config");

const rtdb = firebase.database();

/**
 * 抓取 Firebase Realtime Database 資料
 * @param {string} rtCounter - realtime database counter key (e.g., 'tltc', 'tltc2')
 * @returns {Promise<Object>} Firebase data object
 */
const getFbData = async (rtCounter) => {
    try {
        const snapshot = await rtdb.ref(`counter/${rtCounter}`).once("value");
        const fbData = snapshot.val();

        if (!fbData) {
            console.log("No Firebase data found for counter:", rtCounter);
            return null;
        }

        console.log("Firebase data retrieved for counter:", rtCounter);
        return fbData;
    } catch (error) {
        console.error("Error fetching Firebase data:", error);
        throw error;
    }
};

/**
 * 取得特定 key 的 Firebase 資料
 * @param {string} rtCounter - realtime database counter key
 * @param {string} dataKey - specific data key to retrieve (e.g., 'hotBooks', 'hotPerson', 'visits')
 * @returns {Promise<Object>} Specific data from Firebase
 */
const getFbDataByKey = async (rtCounter, dataKey) => {
    try {
        const fbData = await getFbData(rtCounter);

        if (!fbData || !fbData[dataKey]) {
            console.log(
                `No data found for key '${dataKey}' in counter '${rtCounter}'`
            );
            return null;
        }

        return fbData[dataKey];
    } catch (error) {
        console.error(
            `Error fetching Firebase data for key '${dataKey}':`,
            error
        );
        throw error;
    }
};

/**
 * 檢查是否需要重設資料
 * @param {Object} fbData - Firebase data object
 * @param {string} rtResetKey - reset key name (default: 'reset')
 * @returns {boolean} true if reset is needed
 */
const shouldResetData = (fbData, rtResetKey = "reset") => {
    if (!fbData) {
        return true;
    }

    if (!Object.prototype.hasOwnProperty.call(fbData, rtResetKey)) {
        return true;
    }

    if (fbData[rtResetKey] === true) {
        return true;
    }

    return false;
};

/**
 * Route handler for getting Firebase data
 * GET /hotBooks/:rtCounter - 取得完整 Firebase 資料
 * GET /hotBooks/:rtCounter/:dataKey - 取得特定 key 的 Firebase 資料
 */
const handleHotBooksRoute = async (req, res) => {
    try {
        const { rtCounter, dataKey } = req.params;

        if (!rtCounter) {
            return res.status(400).json({
                success: false,
                message: "rtCounter parameter is required",
            });
        }

        let result;

        if (dataKey) {
            // 取得特定 key 的資料
            result = await getFbDataByKey(rtCounter, dataKey);
        } else {
            // 取得完整資料
            result = await getFbData(rtCounter);
        }

        return res.status(RESPONSE_OK).json({
            success: true,
            data: result,
        });
    } catch (error) {
        console.error("Error in handleHotBooksRoute:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
};

module.exports = {
    getFbData,
    getFbDataByKey,
    shouldResetData,
    handleHotBooksRoute,
};
