const { baseIri } = require("../rdf");
const { myProperty } = require("./myProperty");
const { isValidEntry } = require("../../common/common");
const defines = require("./bindIdDefine");

// 只處理 Dummy 的 SubValue，如：新舊組織
// OrgA isReplacedBy OrgB
exports.dummyType = (classType, value, isAdd) => {
    const resObj = { dummyProcessed: "", dummyWhere: "" };
    // only handle dummy type
    if (classType !== defines._DUMMY) {
        return resObj;
    }

    Object.keys(value).forEach((property) => {
        const subValue = value[property];

        // The values should be filled.
        if (!isValidEntry(subValue)) {
            return;
        }
        // sub values
        const { srcId: tmpSrcId } = subValue;
        const srcId = Array.isArray(tmpSrcId) ? tmpSrcId[0] : tmpSrcId;

        resObj.dummyProcessed += myProperty(subValue, baseIri(srcId), isAdd);
    });
    // where is for sparql use, and it means nothing in this part.
    const svBindId = `${defines._bindAddId}_${classType}`;
    resObj.dummyWhere += `BIND(IRI(${baseIri(
        defines._DUMMY
    )}) AS ?${svBindId}) .`;

    return resObj;
};
