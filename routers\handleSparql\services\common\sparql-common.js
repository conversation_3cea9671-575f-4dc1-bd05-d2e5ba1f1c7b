const storeDriver = require("../sparql/storeDriver");
const { removeBindingsPrefix } = require("../sparql/rdf");
const { getLanguageTag, replaceQueryParams } = require("./common");
const {
    FEATURE_STORE,
    FEATURE_STORE_LIST,
} = require("../../../../config/config");
const { getUpdateLinks } = require("../../../handleAuth/apiObj");

exports.doMakeApiQuery = (apiQuery, limit, offset) => {
    let newApiQuery = apiQuery;

    return new Promise((resolve, reject) => {
        storeDriver
            // .makeQuery(query, limit, offset, reasoning)
            .makeQuery(newApiQuery, limit, offset)
            .then((response) => {
                const bindings = removeBindingsPrefix(
                    response.body.results.bindings
                );
                resolve({ data: bindings, head: response.body.head.vars });
            })
            .catch((error) => {
                reject({ error });
            });
    });
};

exports.doMakeCountQuery = (apiQuery) => {
    // Search count 以帶過來的參數決定要不要取
    if (FEATURE_STORE_LIST.fuseki === FEATURE_STORE) {
        return null;
    }
    let countQuery = `SELECT (COUNT(DISTINCT *) AS ?total) WHERE { { ${apiQuery} } }`;

    return new Promise((resolve, reject) => {
        storeDriver
            // .makeQuery(query, limit, offset, reasoning)
            .makeQuery(countQuery, -1, 0)
            .then((response) => {
                const bindings = removeBindingsPrefix(
                    response.body.results.bindings
                );
                resolve(bindings[0]);
            })
            .catch((error) => {
                reject({ error });
            });
    });
};

// 處理特殊字元,避免 sparql query 抱錯
const safeSparqlStr = (str) => {
    const split = (str || "").split("");
    const replace = [
        { from: "\\", to: "\\\\" },
        { from: "'", to: "\\'" },
    ];
    return split
        .map((s) => {
            let tmpS = s;
            replace.forEach(({ from, to }) => {
                tmpS = tmpS.replace(from, to);
            });
            return tmpS;
        })
        .join("");
};

exports.doSaprqlQuery = (queryStr) => {
    return new Promise((resolve, reject) => {
        storeDriver
            .makeQuery(queryStr)
            .then((response) => {
                if (response.status !== 200) {
                    console.log(response);
                }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.doSaprqlQueryAsync = async (queryStr, limit, offset) => {
    return new Promise(async (resolve, reject) => {
        await storeDriver
            .makeQuery(queryStr, limit, offset)
            .then((response) => {
                // if (response.status !== 200) {
                //     console.log(response);
                // }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.doSaprqlUpdate = async (queryStr) => {
    return new Promise(async (resolve, reject) => {
        await storeDriver
            .makeUpdate(queryStr)
            .then((response) => {
                if (response.status !== 200) {
                    console.log(response);
                }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.doSplitMultipleValues = (value, opName, subjectId) => {
    let queryStr = "";

    // 如果為 null，則為刪除，不填值
    if (!value) {
        return "";
    }

    // Rule: Array is an array, string is a string.
    if (Array.isArray(value)) {
        value.forEach((an) => {
            // 如果為 null，則為刪除，不填值
            if (!an) {
                return;
            }

            const [pureLabel, langTag] = getLanguageTag(an);
            queryStr += `${subjectId} ${opName} '''${safeSparqlStr(
                pureLabel
            )}'''${langTag} .`;
        });
    } else {
        const [pureLabel, langTag] = getLanguageTag(value);
        queryStr += `${subjectId} ${opName} '''${safeSparqlStr(
            pureLabel
        )}'''${langTag} .`;
    }
    return queryStr;
};

exports.doDeleteEmptyLinks = async (srcId) => {
    const apiVer = getUpdateLinks();
    const apiQuery = replaceQueryParams(
        apiVer.query,
        { srcId },
        apiVer.key,
        "en"
    );

    return new Promise(async (resolve, reject) => {
        await storeDriver
            .makeUpdate(apiQuery)
            .then((response) => {
                if (response.status !== 200) {
                    console.log(response);
                }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.safeSparqlStr = safeSparqlStr;
