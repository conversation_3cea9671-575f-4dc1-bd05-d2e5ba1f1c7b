const { baseIri } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const mergeSparql = (iriSrc, iriDst) => `
DELETE {
    GRAPH ?g {
        ?s ?p ?o .
    }
}
INSERT {
    GRAPH ?g {
        ?newS ?newP ?newO .
    }
}
WHERE {
    GRAPH ?g {
        {
            BIND(${iriSrc} AS ?s) .
            ?s ?p ?o .
            BIND(${iriDst} AS ?newS) .
            BIND(?p AS ?newP) .
            BIND(?o AS ?newO) .
        }
        UNION 
        {
            BIND(${iriSrc} AS ?o) .
            ?s ?p ?o .
            BIND(?s AS ?newS) .
            BIND(?p AS ?newP) .
            BIND(${iriDst} AS ?newO) .
        }
    }
}
`;

const mergeInstances = async (entrySrc, entryDst) => {
    const _queryStr = mergeSparql(
        baseIri(entrySrc.srcId),
        baseIri(entryDst.srcId)
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.mergeInstances20 = async (entrySrc, entryDst, callback) => {
    await Promise.all([mergeInstances(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
