const accumulateCounter = require("./counterClient");

// counterAop middleware
const counterAop = (req, res, next) => {
    // get url
    const url = req.path;

    // Search Bar
    // about: fiction & person(author) & publication
    // e.g. /search/literary/1.0?limit=-1&offset=0&ds=hor&keyword=女鬼
    if (url === "/search/literary/1.0" || url === "/search/data/1.0") {
        accumulateCounter(req.query.keyword);
        return next();
    }

    // Book Info
    // about: publication
    // e.g. publication/info/1.0?limit=-1&offset=0&ds=hor&id=PUB51795
    if (url === "/publication/info/1.0") {
        accumulateCounter(req.query.id);
        return next();
    }

    next();
};

module.exports = counterAop;
