const firebase = require("../../config/config.firebase");
const { apiCollDocWriteSync } = require("./collDocWriteSync");
const firestoreDb = firebase.firestore();
const realtimeDb = firebase.database();

module.exports = {
    async dailyUpdateCount() {
        if (!realtimeDb) {
            return;
        }

        const nowDate = new Date();
        // 2024-06-19
        const ymd = nowDate.toISOString().split("T")[0];

        const dailyCount = { time: Date.now(), date: ymd };
        await realtimeDb
            .ref(`counter`)
            .once("value")
            .then((snapshot) => {
                // {
                // tltc2:
                // {
                //      visits:{all:123, modified:"...", today: 2},
                //      hotArticles:{...},
                //      hotBooks:{...},
                //      keyword:{...},
                //      reset:false
                // } ...}
                const fbData = snapshot.val();
                Object.keys(fbData).forEach((prj) => {
                    if (prj === "undefined") {
                        return;
                    }

                    const { visits } = fbData[prj];
                    if (!visits) {
                        return;
                    }
                    // console.log(prj, visits);
                    const { all, today } = visits;
                    if (!all) {
                        return;
                    }
                    const newToday = today || 0;
                    dailyCount[prj] = all + newToday;
                });
            });
        await apiCollDocWriteSync(firestoreDb, "daily-count", dailyCount);
    },
};
