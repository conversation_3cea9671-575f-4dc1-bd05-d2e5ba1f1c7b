{"parser": "babel-es<PERSON>", "extends": ["standard", "plugin:react/recommended", "prettier", "prettier/react", "prettier/standard"], "plugins": ["react", "prettier", "standard"], "parserOptions": {"sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"es6": true, "node": true}, "globals": {"fetch": true}, "rules": {"prettier/prettier": ["error", {"tabWidth": 4, "endOfLine": "auto"}], "indent": ["error", 4, {"SwitchCase": 1}]}}