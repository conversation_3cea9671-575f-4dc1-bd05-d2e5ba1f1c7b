const { graphIri } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");


// 複製資料到其他graph，只複製id
const copySparql = (oldGraph, newGraph, srcId) => {
    return `
            INSERT {
                GRAPH  ${newGraph} {
                    nmtl:${srcId} a ?type .
                }
            }
            WHERE {
                GRAPH ${oldGraph} {
                    nmtl:${srcId} a ?type .
                }
            };
          `;
};

const copyInstances = (entrySrc, entryDst) => {
    const _queryStr = copySparql(
        graphIri(entrySrc.value),
        graphIri(entryDst.value),
        entryDst.srcId
    );

    return doSaprqlUpdate(_queryStr);
};

exports.updateCopyInstancesOnlyId20 = (entrySrc, entryDst, callback) => {
    Promise.all([copyInstances(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
