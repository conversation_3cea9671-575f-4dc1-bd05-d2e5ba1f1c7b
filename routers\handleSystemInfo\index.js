const si = require("systeminformation");

const systemInfo = async (req, res, next) => {
    // init
    const sysInfo = {
        cpu: 0,
        mem: 0,
        disk: 0,
        rx_bytes: 0,
        rx_dropped: 0,
        rx_errors: 0,
        tx_bytes: 0,
        tx_dropped: 0,
        tx_errors: 0,
    };

    try {
        // cpu
        await si.currentLoad().then((data) => {
            const cpuPercent = data.currentload || 0;
            sysInfo.cpu = Math.round(cpuPercent * 100) / 100;
        });
        // memory
        await si.mem().then((data) => {
            const memPercent = (data.used / data.total) * 100 || 0;
            sysInfo.mem = Math.round(memPercent * 100) / 100;
        });
        // fsSize
        await si.fsSize().then((data) => {
            const diskPercent = (data && data[0] && data[0].use) || 0;
            sysInfo.disk = Math.round(diskPercent * 100) / 100;
        });
        // network
        await si.networkStats().then((data) => {
            // Transmit
            const { rx_bytes, rx_dropped, rx_errors } = data && data[0];
            sysInfo.rx_bytes = rx_bytes || 0;
            sysInfo.rx_dropped = rx_dropped || 0;
            sysInfo.rx_errors = rx_errors || 0;
            // Receive
            const { tx_bytes, tx_dropped, tx_errors } = data && data[0];
            sysInfo.tx_bytes = tx_bytes || 0;
            sysInfo.tx_dropped = tx_dropped || 0;
            sysInfo.tx_errors = tx_errors || 0;
        });
    } catch (e) {
        console.log(e);
    }

    return res.json(sysInfo);
};

module.exports = systemInfo;
