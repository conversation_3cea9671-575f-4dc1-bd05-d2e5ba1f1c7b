const express = require("express");
const router = express.Router();
const upload = require("../../commons/multer");
const { UPLOAD_FIELD } = require("../../config/config-upload");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");
const importMain = require("./importMain");
const {redisFlushCache} = require("../handleRedis/handleRedis");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    return verifyToken(authToken)
        .then(() => {
            next();
        })
        .catch((error) => {
            console.log(error);
            next(new CustomError(UNAUTHORIZED));
        });
};

/**
 * e.g. import api
 * 1. http://localhost:3000/import/file/tltc
 * 2. http://api2.daoyidh.com/import/file/ra
 * */
router.post(
    "/file/:pattern",
    authHandler,
    upload(UPLOAD_FIELD.file.name).single(UPLOAD_FIELD.file.name),
    redisFlushCache,
    importMain
);

module.exports = router;
