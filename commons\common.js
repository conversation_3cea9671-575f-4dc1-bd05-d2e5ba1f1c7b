const { UPLOAD_FIELD } = require("../config/config-upload");
const path = require("path");
const fs = require("fs");

const getOs = () => (process.platform === "win32" ? "windows" : "linux");

const splitFileNameExt = (fileName) => {
    const reg = /^(.+?)(?=\.\w+$|$)/;
    const [fName] = fileName.split(reg).filter((str) => str);

    return fName;
};

const getTargetPath = (type, fieldName) => {
    let dir = "";
    if (!(type && type in UPLOAD_FIELD[fieldName].dir)) {
        dir = UPLOAD_FIELD[fieldName].dir.default;
    } else {
        dir = UPLOAD_FIELD[fieldName].dir[type];
    }

    return getOs() === "windows"
        ? path.join(process.env.FILE_SERVER_DST_WIN, dir || "")
        : path.join(process.env.FILE_SERVER_DST_LINUX, dir || "");
};

const getFiles = (path) => {
    const entries = fs.readdirSync(path, { withFileTypes: true });
    const files = entries.filter((folder) => !folder.isDirectory());
    return files.map((f) => f.name);
};

const getFileNameFromPath = (pathStr) => {
    return getOs() === "windows"
        ? pathStr.split("\\").pop()
        : pathStr.split("/").pop();
};

module.exports = {
    getOs,
    splitFileNameExt,
    getTargetPath,
    getFiles,
    getFileNameFromPath,
};
