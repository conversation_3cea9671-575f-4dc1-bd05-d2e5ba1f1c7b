const { baseIri, rdfsIri } = require("../rdf");
const {
    DateEventType,
    classPrefix,
    PropertyAClass,
} = require("../classPrefix");
const {
    createDateEvent,
    deleteDateEvent,
} = require("../classes/makeDateEvent");
const {
    isSubValue,
    isValidEntry,
    replIllegalIRIChar,
} = require("../../common/common");
const {
    isEventType,
    getTypeFromPrefix,
    findInverseRelation,
    getTypeRangeFromProperty,
    isCorrectProperty,
} = require("./common");
const { doSplitMultipleValues } = require("../../common/sparql-common");
const defines = require("./bindIdDefine");

const _ValueSTARTS = defines._ValueSTARTS;

exports.personObj = (entry) => baseIri(entry.srcId);

exports.myProperty = (entry, bindId, isAdd) => {
    let qryStr = "";

    if (!isValidEntry(entry)) {
        return qryStr;
    }

    // { graph: 'hkwrpr',
    //   classType: 'relationevent',
    //   value: { hasAdvisor: [ 'PER丁仁長' ] },
    //   srcId: 'PER金庸' }
    const { classType, value } = entry;

    // 處理 data property 的類型
    qryStr += createProperty(classType, bindId, value, isAdd);
    return qryStr;
};

const createPropertyValue = (bindId, opObj, value, range, isAdd) => {
    let sObj = "";

    // 空值
    if (value === "") {
        return sObj;
    }

    // DateEvent
    if (DateEventType === range) {
        // 新增 DateEvent 的同時要產生 year, month, day
        // 刪除 DateEvent 的時候，只要刪除與 DateEvent 的連結即可
        if (isAdd) {
            sObj = createDateEvent(bindId, opObj, value, range, "DAE");
        } else {
            sObj = deleteDateEvent(bindId, opObj, value, "DAE", isAdd);
        }
        return sObj;
    }

    // 為變數形態，直接 assgin
    // 如：?randomId_EducationEvent
    if (value.startsWith("?")) {
        return `${bindId} ${opObj} ${value} .`;
    }

    const foundp = getTypeFromPrefix(value);
    const prefix = foundp ? foundp.prefix : "";

    // 接受帶過來的參數有 prefix 或沒有
    // PER高雄 or 高雄
    if (value.startsWith(prefix)) {
        sObj = baseIri(replIllegalIRIChar(value));
    } else {
        sObj = baseIri(replIllegalIRIChar(`${prefix}${value}`));
    }

    return `${bindId} ${opObj} ${sObj} .`;
};

function createProperty(classType, bindId, valueObj, isAdd) {
    const eventObj = isEventType(classType);

    let qryStr = "";
    if (isAdd) {
        qryStr += `${bindId} a ${baseIri(classType)} .`;
    }
    Object.keys(valueObj).forEach((prop) => {
        let value = valueObj[prop];

        // ---- SubValue 也可能是這種型式 ----
        // 後面處理
        // [
        //   {
        //     srcId: 'SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_141',
        //     classType: 'Source',
        //     value: {
        //       label_Source: '111111111'
        //     }
        //   },
        //   {
        //     srcId: '',
        //     classType: 'Source',
        //     value: {
        //       label_Source: '22222222'
        //     }
        //   }
        // ]
        if (isSubValue(value)) {
            return;
        }

        // ----後面統一轉成 array 處理----
        if (!Array.isArray(value)) {
            value = [value];
        }

        // label
        const labelProperty = `${_ValueSTARTS}${classType}`;
        if (prop === labelProperty || prop === "label") {
            const label = prop.includes("_") ? prop.split("_")[0] : "label";
            const opObj = rdfsIri(label);

            // rdfs:label's value 不需要有 prefix
            let newValue = value.map((v) => {
                // 若為 null，代表刪除，不填值
                if (!v) {
                    return;
                }

                // let foundPrefix = classPrefix.find((item) =>
                //     v.startsWith(item.prefix)
                // );

                // if (foundPrefix) {
                //     return v.replace(foundPrefix.prefix, "");
                // }
                return v;
            });
            value = [...newValue];

            qryStr += doSplitMultipleValues(value, opObj, bindId);
            return;
        }

        // rdf:type
        // nmtl:PUBxxxx a nmtl:PSSingleCopy
        if (PropertyAClass.indexOf(prop) > -1) {
            const rdfType = "rdf:type";

            value.forEach((val) => {
                const sObj = baseIri(replIllegalIRIChar(val));
                qryStr += `${bindId} ${rdfType} ${sObj} .`;
            });
            return;
        }

        const propName = prop.split("__")[0];
        const typeArr = getTypeRangeFromProperty(classType, propName);
        if (!typeArr || typeArr.length < 1) {
            console.error("typeArr.length < 1::", propName, typeArr);
            // typeArr.length > 2 only happens in object property.
            return;
        }

        const [type, range] = typeArr[0].split(defines._PropertySplitter);
        // data property
        if (type === "data") {
            const opObj = baseIri(propName);
            value.forEach((val) => {
                // 帶過來的值可能是 null，代表刪除
                if (!val) {
                    return;
                }

                if (range === "float") {
                    qryStr += `${bindId} ${opObj} '''${val}'''^^xsd:float .`;
                } else if (range === "integer") {
                    qryStr += `${bindId} ${opObj} '''${val}'''^^xsd:integer .`;
                } else if (range === "double") {
                    qryStr += `${bindId} ${opObj} '''${val}'''^^xsd:double .`;
                } else {
                    qryStr += doSplitMultipleValues(val, opObj, bindId);
                }
            });
            return;
        }

        // object property
        if (type === "object") {
            const opObj = baseIri(propName);
            value.forEach((val) => {
                // 帶過來的值可能是 null，代表刪除
                if (!val) {
                    return;
                }

                let newRange = range;
                if (typeArr.length > 1) {
                    // eq: hasFounded, hasParticipant -> Organization and PoetClub
                    // 以 value 的 prefix type 為主
                    const newTypeObj = getTypeFromPrefix(val);
                    if (!newTypeObj) {
                        console.error(
                            "ERROR!! this value should have the type.",
                            val
                        );
                        return;
                    }
                    newRange = newTypeObj.eventType;
                }

                qryStr += createPropertyValue(
                    bindId,
                    opObj,
                    val,
                    newRange,
                    isAdd
                );

                // event 的反關係
                // if (eventObj && val !== "") {
                // 20221214, Vincent: 不一定 event 才有反關係, 需要全部處理.
                if (val !== "") {
                    // 如果 property 為 event，需要多設定 inverse 關係
                    const inverseOpObj = findInverseRelation(propName);
                    if (!inverseOpObj || inverseOpObj === "") {
                        // 無需增加反關係
                        return;
                    }

                    // 極特殊情況，inverse 關係並不屬於此 Class
                    // 如：Person hasSpecialty SpecialtyEvent
                    //    SpecialtyEvent hasSpecialty Specialty
                    // 但是 Specialty 並沒有 inverse 關係 SpecialtyEvent
                    // 以 hasSpecialty 皆可找到 inverse 關係 isSpecialtyOf
                    if (!isCorrectProperty(newRange, inverseOpObj)) {
                        return;
                    }

                    const iriInvOpObj = baseIri(inverseOpObj);
                    const iriVal = baseIri(val);

                    // Create 的時候宣告 ClassType, 但是刪除時不能刪
                    if (isAdd) {
                        qryStr += `${iriVal} a ${baseIri(newRange)} .`;
                    }
                    qryStr += createPropertyValue(
                        iriVal,
                        iriInvOpObj,
                        bindId,
                        classType,
                        isAdd
                    );
                }
            });
        }
    });

    return qryStr;
}
