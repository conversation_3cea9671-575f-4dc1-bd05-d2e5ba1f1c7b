const { Connection, query, db } = require("stardog");
const {
    DEFAULT_MAX_LIMIT,
    DEFAULT_OFFSET,
} = require("../../../../../config/config");
let myConn = null;
let dbName = null;

module.exports = {
    getDbName() {
        return dbName;
    },
    setSparql(settings) {
        if (!settings) {
            return;
        }

        /*
        const { user, password, url, db } =
            process.env.NODE_ENV === "production"
                ? settings
                : require("../../../../config/config.dev");
         */

        const {
            user,
            password,
            url,
            db,
        } = require("../../../../../config/config.stardog");

        dbName = db;
        myConn = new Connection({
            username: user,
            password: password,
            endpoint: url,
        });
    },
    makeQuery(
        queryString,
        limit = DEFAULT_MAX_LIMIT,
        offset = DEFAULT_OFFSET,
        reasoning = false
    ) {
        if (dbName === null) {
            return null;
        }
        return query.execute(
            myConn,
            dbName,
            queryString,
            "application/sparql-results+json",
            {
                limit,
                offset,
                reasoning,
            }
        );
    },
    // 匯出某個資料庫的某個 graph 及其內的 triple
    exportDbGraph(exportDbName, exportGraph, mineType) {
        if (!(exportDbName && exportGraph && mineType))
            return Promise.resolve(
                "lack of exportDbName or exportGraph or mineType"
            );
        if (!myConn) return Promise.resolve(null);

        // response.body => 不管 mineType 是 application/trig 或 application/json
        // 皆為 object
        return db.exportData(
            myConn,
            dbName || exportDbName,
            { mineType: mineType },
            {
                graphUri: exportGraph,
            }
        );
    },
};
