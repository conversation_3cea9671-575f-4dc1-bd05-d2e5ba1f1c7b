const {
    ALLOWED_DOMAIN,
    ALLOWED_GOV_DOMAIN,
    ALLOWED_ORIGINS,
} = require("../../config/config");

const safeHeader = (req, res, next) => {
    // console.dir('safeHeader start');
    if (req && req.headers && req.headers.origin) {
        const { origin } = req.headers;
        // domain to pass
        if (
            origin.endsWith(ALLOWED_DOMAIN) ||
            origin.endsWith(ALLOWED_GOV_DOMAIN)
        ) {
            res.setHeader("Access-Control-Allow-Origin", origin);
        } else if (ALLOWED_ORIGINS.indexOf(origin) > -1) {
            res.setHeader("Access-Control-Allow-Origin", origin);
        }
    }

    // Request methods you wish to allow
    res.setHeader(
        "Access-Control-Allow-Methods",
        "GET, POST, OPTIONS, PATCH, PUT, DELETE"
    );

    // Request headers you wish to allow
    res.setHeader(
        "Access-Control-Allow-Headers",
        "X-Requested-With,content-type,Authorization"
    );

    // Set to true if you need the website to include cookies in the requests sent
    // to the API (e.g. in case you use sessions)
    res.setHeader("Access-Control-Allow-Credentials", true);

    // console.dir('safeHeader end');
    // Pass to next layer of middleware
    next();
};

module.exports = safeHeader;
