MODE=production
FILE_SERVER_DST_LINUX=/mnt/nmtl-static-files
FILE_SERVER_DST_WIN=C:/mnt/nmtl-static-files

NMTL_API_NODE=https://api3.daoyidh.com/nmtl
#NMTL_API_NODE=https://api2.daoyidh.com/nmtl2

#檔案上傳路徑
WINDOWS_UPLOAD_IMAGE_DIRECTORY=C:/nmtl-static-files/images/original/imgupload
LINUX_UPLOAD_IMAGE_DIRECTORY=/mnt/nmtl-static-files/original/imgupload
WINDOWS_UPLOAD_DOCS_DIRECTORY=C:/nmtl-static-files/files/src/upload
LINUX_UPLOAD_DOCS_DIRECTORY=/mnt/nmtl-static-files/files/src/upload

# 讀取圖片的 file server domain
FILE_SERVER_API_DOMAIN=https://fs-root.daoyidh.com

# atai AI
NMTL_ATAI_API_NODE=https://api.daoyidh.com/genaipdf/webhook
#NMTL_ATAI_API_NODE=https://api2.daoyidh.com/genaipdf/webhook

# 需要 debug 時, 設定 true, console.log 會印出
NODE_DEBUG=false

# for nmtl to update hot search 
REALTIME_DATABASE_MODE=production_nmtlGov

# for daoyi to update hot search 
# REALTIME_DATABASE_MODE=production