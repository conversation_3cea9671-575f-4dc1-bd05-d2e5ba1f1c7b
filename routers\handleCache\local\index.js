const { cacheTime } = require("../../../config/config.counter");

let cache = {};

// clean cache
setInterval(() => {
    for (let [k, v] of Object.entries(cache)) {
        if (v.ttl <= Date.now()) {
            delete cache[k];
        }
    }
}, 5 * 1000);

const setCache = (key, value, ttl = cacheTime) => {
    cache[key] = { data: value, ttl: Date.now() + ttl };
};

const getCache = (key) => {
    return cache[key] && cache[key]["data"];
};

module.exports = {
    setCache,
    getCache,
};
