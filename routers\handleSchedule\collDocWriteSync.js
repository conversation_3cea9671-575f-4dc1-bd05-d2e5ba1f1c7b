module.exports = {
    /**
     * 寫入 collection 中的單一文件
     *
     * @param firestoreDb => firebase.firestore()
     * @param collectionName => firestore collectionName, e.g. "api"
     * @param docName => firestore docName, e.g. "read"
     * @param callback (optional)=> func.
     */
    async apiCollDocWriteSync(
        inFirestoreDb,
        collectionName,
        dataObj,
        callback
    ) {
        if (!(inFirestoreDb && collectionName && dataObj)) {
            const errMsg = {
                error: "Parameters required in apiCollDocListener(): firestoreDb, collectionName, docName",
            };
            if (callback) {
                callback(null, errMsg);
            }
            return;
        }

        const res = inFirestoreDb.collection(collectionName).add(dataObj);
        if (callback) {
            callback(null, res);
        }
    },
};
