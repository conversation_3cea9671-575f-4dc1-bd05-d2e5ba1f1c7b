const path = require("path");

const envFile = {
    production_tltc: ".env.production_tltc",
    production: ".env.production",
    development: ".env.development",
    default: ".env",
};
const getEnvFile = (NODE_ENV) => {
    if (!NODE_ENV) return envFile.default;
    return envFile[NODE_ENV];
};

const useEnv = () => {
    // eslint-disable-next-line no-undef
    const envPath = path.resolve(
        process.cwd(),
        getEnvFile(process.env.ENV || process.env.NODE_ENV)
    );
    require("dotenv").config({ path: envPath });
};

module.exports = useEnv;
