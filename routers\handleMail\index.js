const nodemailer = require("nodemailer");
const emailConfig = require("../../config/config.email");
const {
    RESPONSE_OK,
    MAIL_SENT,
    ERROR_WRONG_PARAMS,
} = require("../../config/config");

async function handleMail(req, res, next) {
    const { sendMail } = req.body;

    if (!sendMail.hasOwnProperty("from") || !sendMail.hasOwnProperty("to")) {
        return res.status(RESPONSE_OK).send(ERROR_WRONG_PARAMS);
    }

    // create reusable transporter object using the default SMTP transport
    const transporter = nodemailer.createTransport(emailConfig);

    // send mail with defined transport object
    const _info = await transporter.sendMail(sendMail);

    return res.status(RESPONSE_OK).send(MAIL_SENT);
}

module.exports = handleMail;
