const fs = require("fs");
const Fs = fs.promises;
//
const xlsx = require("node-xlsx");
const timerHelper = require("./timerHelper");
const errorHelper = require("./errorHelper");
const {
    splitFileNameExt,
    getTargetPath,
    getFiles,
    getFileNameFromPath,
} = require("../../../commons/common");
const path = require("path");
const {
    originDir,
    originDocDir,
} = require("../../../config/config.fileServer");
const { fileType } = require("../../../config/config");
//

const deepClone = (object) => {
    return JSON.parse(JSON.stringify(object));
};

const STORE_FILE_MESSAGE = {
    success: "success",
    fail: "fail",
};

const fileHelper = {
    STORE_FILE_MESSAGE: STORE_FILE_MESSAGE,
    // 確認檔案是否存在
    existsFile: async (path) => {
        try {
            await Fs.access(path);
            return true;
        } catch (err) {
            return false;
        }
    },
    // read excel file
    // return: [
    //  {
    //      name: 'Sheet1',
    //      data: [
    //          [Array], [Array],
    //          [Array], [Array],
    //          [Array], [Array],
    //          [Array], [Array]
    //      ]
    //  }
    // ]
    readExcelFile: async (path) => {
        try {
            // Parse a file
            // [{
            //     name: 'Sheet1',
            //     data: [
            //       [Array], [Array], [Array], [], [], []
            //     ]
            // }]
            const workSheetsFromFile = await xlsx.parse(path);

            if (workSheetsFromFile) {
                // 移除 data 中的空白陣列
                const sheetDataRmBlank = workSheetsFromFile.reduce(
                    (acc, cur, idx) => {
                        if (cur && Array.isArray(cur.data)) {
                            acc[idx] = {};
                            acc[idx].name = cur.name;
                            acc[idx].data = cur.data.filter(
                                (dt) => Array.isArray(dt) && dt.length > 0
                            );
                        }
                        return acc;
                    },
                    []
                );
                return Promise.resolve(sheetDataRmBlank);
            } else {
                return Promise.resolve(false);
            }
        } catch (err) {
            console.log("readExcelFile", err.message);
            return Promise.reject(err.message);
        }
    },
    // read excel file cols
    getExcelCols: (rows) => {
        return rows[0];
    },
    // 比對檔案中的欄位是否與預設的欄位相同
    checkColsEqual: (cols, defaultCols) => {
        return (
            JSON.stringify(deepClone(cols).sort()) ===
            JSON.stringify(deepClone(defaultCols).sort())
        );
    },
    bestKnownNameChecker: (val) => {
        return val && (val.indexOf("@PER") >= 0 || val.indexOf("@ORG") >= 0);
    },
    // check 某個 col 的 value 是否符合設定
    // e.g. bestKnownName 必須是 @PER or @ORG
    validateSingleColVal: (mapList, key, checker) => {
        if (typeof checker !== "function") return true;
        return mapList.every((map) => {
            if (key in map) {
                return checker(map[key]);
            }
            return true;
        });
    },
    // 把 [["","",""],[],[],...] 轉變成 [{},{},...]
    arrayToMapList: (rows, headers) => {
        if (!(Array.isArray(rows) && Array.isArray(headers))) return [];
        return rows.reduce((acc, cur, idx) => {
            const curMap = headers.reduce((_acc, header, hIdx) => {
                _acc[header] = cur[hIdx];
                return _acc;
            }, {});
            acc.push(curMap);
            return acc;
        }, []);
    },
    getFilePath: (req) => {
        const { targetPath } = req;
        if (Array.isArray(targetPath) && targetPath[0]) {
            return targetPath[0];
        }
        return null;
    },
    // 取得檔案名稱
    getFileName: (req) => {
        const { targetPath } = req;
        let fileName = "";
        if (Array.isArray(targetPath) && targetPath[0]) {
            fileName = getFileNameFromPath(targetPath[0]);
        }

        return fileName;
    },
    // 確認要處理哪個檔案及檔案是否存在
    // - check req.fileValidation, req.targetPath 及是否有成功上傳
    fileValidate: async (req) => {
        const { targetPath } = req;
        if (Array.isArray(targetPath) && targetPath[0]) {
            const exist = await fileHelper.existsFile(targetPath[0]);
            return Promise.resolve(exist);
        }
        return Promise.resolve(false);
    },
    // store file to path
    writeFile: async (dataBuffer, fileDst) => {
        try {
            const startTime = new Date();
            timerHelper.timeStart("writeFile", startTime);

            // flag: 'w' => Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
            const err = await fs.promises.writeFile(fileDst, dataBuffer, {
                flag: "w",
            });
            if (err) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
                );
            } else {
                timerHelper.timeStampEnd("writeFile", startTime);
                return { status: STORE_FILE_MESSAGE.success };
            }
        } catch (err) {
            console.log("error in writeFile", err.message);
            return err;
        }
    },
    // copy from file-server feature
    getFolderDocs: (folder) => {
        try {
            if (folder.indexOf(".") > -1) {
                return {
                    status: "error",
                    message: "folder is not acceptable.",
                };
            }
            const realFolder = `${originDocDir}/${folder}`;

            if (!fs.existsSync(realFolder)) {
                // mkdir dir if it not found
                fs.mkdir(realFolder, { recursive: true }, () => {});
            }

            const files = getFiles(realFolder);

            return { data: files && files.length === 0 ? [] : files };
        } catch (err) {
            // there is no such folder
            return {
                status: "error",
                message: err.message,
            };
        }
    },
    // copy from file-server feature
    getFolderImages: (folder) => {
        try {
            if (folder.indexOf(".") > -1) {
                return {
                    status: "error",
                    message: "folder is not acceptable.",
                };
            }
            const realFolder = path.join(originDir, folder);
            // `${originDir}/${folder}`;

            if (!fs.existsSync(realFolder)) {
                // mkdir dir if it not found
                fs.mkdir(realFolder, { recursive: true }, () => {});
            }

            const filesName = getFiles(realFolder);

            return {
                data: filesName && filesName.length === 0 ? [] : filesName,
            };
        } catch (err) {
            // there is no such folder
            return {
                status: "error",
                message: err.message,
            };
        }
    },
    // copy from file-server feature
    getNmtlFolderFiles: (folder, type) => {
        if (type === fileType.image) {
            return fileHelper.getFolderImages(folder);
        } else if (type === fileType.docs) {
            return fileHelper.getFolderDocs(folder);
        } else {
            return {
                status: "error",
                message: "No such type",
            };
        }
    },
    getFinishFileName: (req, uploadStatus) => {
        const { type } = req.query;
        const fileName = fileHelper.getFileName(req);
        const fsName = splitFileNameExt(fileName);
        const newFileName = `${fsName}-${uploadStatus}.${fileName
            .split(".")
            .pop()}`;
        const newTargetPath = path.join(
            getTargetPath(type, "file"),
            newFileName
        );

        return newTargetPath || "";
    },
};

module.exports = fileHelper;
