const { classPrefix } = require("../sparql/classPrefix");
const { doSaprqlQueryAsync } = require("./sparql-common");
const { getReservedCurId, getMaxId } = require("../../../handleAuth/apiObj");
const createApiList = require("../crud/createApis");

exports.getReservedNewId = async (classType) => {
    if (!classType) {
        return null;
    }
    // 先確認這個 classType 是否有 prefix
    const found = classPrefix.filter((cl) => cl.eventType === classType);
    if (!found || found.length === 0) {
        return null;
    }

    const regPrefix = new RegExp(`\\[prefix\\]`, "g");
    const regClassName = new RegExp(`\\[classname\\]`, "g");

    // 取回目前最大值
    const curMaxData = await doSaprqlQueryAsync(
        getReservedCurId().replace(regPrefix, found[0].prefix),
        0,
        0
    );

    const maxValueId = curMaxData?.results.bindings[0].maxValue.value;
    if (!maxValueId || maxValueId === "0") {
        // FIXME: Vincent, 希望可以動態計算最大值並填入 max_xxx
        // 沒有這個 property 存在，試著取最大值
        const oldMaxData = await doSaprqlQueryAsync(
            getMaxId()
                .replace(regClassName, classType)
                .replace(regPrefix, found[0].prefix),
            0,
            0
        );

        const curMaxId = oldMaxData?.results.bindings[0]?.maxNum.value;
        if (curMaxId && curMaxId !== "") {
            // 寫入目前最大值
            // 避免 ID 被 cache，所以帶入 random 值
            await createApiList.newInstance["2.0"](
                { prefix: found[0].prefix, number: curMaxId },
                () => {}
            );
        }
    }

    // 避免 ID 被 cache，所以帶入 random 值
    await createApiList.newInstance["2.0"](
        { prefix: found[0].prefix, number: "1" },
        () => {}
    );

    // 取回目前最大值
    const resData = await doSaprqlQueryAsync(
        getReservedCurId().replace(regPrefix, found[0].prefix),
        0,
        0
    );

    const newValueId = resData.results.bindings[0].maxValue.value;

    // found.eventType
    return `${found[0].prefix}${newValueId}`;
};
