const firebase = require("../../config/config.firebase");
const firestoreDb = firebase.firestore();

// 抓取全部的 Firebase Collections
const firebaseCols = ["api", "frontend-settings", "setting"];

const isEmpty = (obj) => Object.keys(obj || {}).length === 0;

const getSubCollections = async (
    subCol = "mainSubject",
    doc = "dataset",
    col = "setting"
) => {
    const ref = firebase
        .firestore()
        .collection(col)
        .doc(doc)
        .collection(subCol);
    return (
        ref
            // .orderBy("seq", "asc")
            .get()
            .then((querySnapshot) => {
                const sheets = [];
                querySnapshot.forEach((doc) =>
                    sheets.push({ id: doc.id, ...doc.data() })
                );
                return !isEmpty(sheets)
                    ? sheets
                    : { error: "doc or collection doesn't exist" };
            })
    );
};

// FIXME: Vincent
// 目前找不到方法可以自動從 firestore 的 root 取得 sub-documents。
// 暫時先用 getSubCollections() 取得。
const dlFirebaseDb = async (callback) => {
    const allCol = [];

    const mainSubject = await getSubCollections("mainSubject");
    const sheet = await getSubCollections("sheet");

    for (const col of firebaseCols) {
        const colRef = firestoreDb.collection(col);
        const awaitCol = colRef.get();
        allCol.push(awaitCol);
    }

    Promise.all(allCol)
        .then((snapshots) => {
            const apiObj = {};
            snapshots.forEach((ss) => {
                ss.forEach((doc) => {
                    // console.log(doc.id, "=>", doc.data());
                    if (doc.id === "dataset") {
                        apiObj[doc.id] = {};
                        apiObj[doc.id]["mainSubject"] = mainSubject;
                        apiObj[doc.id]["sheet"] = sheet;
                    } else {
                        apiObj[doc.id] = doc.data();
                    }
                });
            });
            callback(apiObj);
        })
        .catch((err) => {
            callback(err);
        });
};

// get setting/upload/imageFolder specific document
const getImageUploadPath = (graph) => {
    const ref = firestoreDb
        .collection("setting")
        .doc("upload")
        .collection("imageFolder")
        .doc(graph);

    return ref.get().then((doc) => {
        const sheets = [];
        if (doc.exists) {
            sheets.push({ id: doc.id, ...doc.data() });
        }
        return !isEmpty(sheets)
            ? sheets
            : { error: "doc or collection doesn't exist" };
    });
};

module.exports = { dlFirebaseDb, getSubCollections, getImageUploadPath };
