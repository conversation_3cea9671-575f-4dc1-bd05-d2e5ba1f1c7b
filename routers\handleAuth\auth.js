const admin = require("firebase-admin");
const apiListener = require("./apiListener");
const sheetListener = require("./sheetListener");
const { removeBindingsPrefix } = require("../handleSparql/services/sparql/rdf");
const storeDriver = require("../handleSparql/services/sparql/storeDriver");

const {
    getAppName,
    setApiObj,
    isApiObjNull,
    getInvertRelation,
    getProperties,
    setApiHelper,
    isProduction,
    isDevelop,
    getToken,
    setSheetObj,
} = require("./apiObj");
// 每個 process 有自己的 admin
let firebaseAdmin = null;

const doMakeApiQuery = (apiQuery, limit, offset) => {
    return new Promise((resolve, reject) => {
        storeDriver
            .makeQuery(apiQuery, limit, offset)
            .then((response) => {
                const bindings = removeBindingsPrefix(
                    response.body.results.bindings
                );
                if (Object.keys(bindings[0]).includes("property")) {
                    resolve({ property: bindings });
                } else {
                    resolve({ relation: bindings });
                }
            })
            .catch((error) => {
                reject({ error });
            });
    });
};

const preGetOntologyValues = () => {
    if (isApiObjNull()) {
        return;
    }
    // get properties from ontology
    const relationInverseQuery = getInvertRelation();
    const propertyQuery = getProperties();

    Promise.all([
        doMakeApiQuery(relationInverseQuery.query, -1, 0),
        doMakeApiQuery(propertyQuery.query, -1, 0),
    ])
        .then((value) => {
            console.log("preGetOntologyValues done.");
            let apiHelper = {};
            value.forEach((v) => {
                apiHelper = Object.assign(apiHelper, v);
            });
            setApiHelper(apiHelper);
            // console.log(apiHelper);
        })
        .catch((err) => {
            console.log(err);
            setApiHelper({});
        });
};

apiListener((api) => {
    if (!api) {
        return;
    }
    setApiObj(api);
    storeDriver.setSparql(api["api-config"]["sparql"]);

    // 先取得所需要的 ontology values
    setTimeout(preGetOntologyValues, 3000);

    // 避免 Firebase 重新 initial
    if (!admin.apps.length) {
        console.log("~~~admin.initializeApp~~~");
        firebaseAdmin = admin.initializeApp(
            {
                credential: admin.credential.cert(
                    api["api-config"]["serviceAccountKey"]
                ),
                databaseURL: "https://nmtl-web.firebaseio.com",
            },
            getAppName()
        );
    }
});

// 取得 sheets 的下載 API
sheetListener((sheetObj) => {
    // console.log(sheetObj);
    setSheetObj(sheetObj);
});

module.exports = {
    async verifyToken(token) {
        // console.log('process.env.NODE_ENV', process.env.NODE_ENV)
        // console.log('process.env.MODE', process.env.MODE)

        if (isApiObjNull()) {
            return Promise.reject(new Error("API not ready."));
        }
        // 正式站使用 firestore doc[api-config].production. true => 代表開啟 token 驗證
        // 測試站使用 firestore doc[api-config].develop. if true => 代表開啟 token 驗證
        const checkMethod = {
            production: isProduction,
            development: isDevelop,
        };
        if (Object.keys(checkMethod).indexOf(process.env.MODE) > 0) {
            const ALLOWED_API_TOKEN = getToken();
            if (checkMethod[process.env.MODE]()) {
                return Promise.resolve();
            } else if (!token) {
                return Promise.reject(new Error("Not Authenticated."));
            } else if (ALLOWED_API_TOKEN.indexOf(token) >= 0) {
                return Promise.resolve();
            }

            console.log(`${process.env.MODE} = true => verifyIdToken(token)`);
            return firebaseAdmin.auth().verifyIdToken(token);
        }
        // process.env.MODE 未設定時, 直接 resolve()
        return Promise.resolve();
    },
};
