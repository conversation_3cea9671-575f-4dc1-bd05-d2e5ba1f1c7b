const { namespace } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const updateFs = async (entrySrc, entryDst) => {
    const { graph, remove, insert, where } = entrySrc;

    let qryDelete = "";
    if (remove && remove !== "") {
        qryDelete = `DELETE {
    GRAPH <${namespace.graph}${graph}> {
        ${remove}
    }
}`;
    }

    let qryInsert = "";
    if (insert && insert !== "") {
        qryInsert = `INSERT {
    GRAPH <${namespace.graph}${graph}> {
        ${insert}
    }
}`;
    }

    const _queryStr = `${qryDelete}${qryInsert} WHERE { ${where} }`;
    // console.log("_queryStr", _queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.updateFs20 = async (entrySrc, entryDst, callback) => {
    await Promise.all([updateFs(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
