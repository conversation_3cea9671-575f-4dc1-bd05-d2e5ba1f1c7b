const { apiUrl } = require("../../config/config.counter");

const accumulateCounter = async (key) => {
    // check parameter
    if (!key) {
        console.warn("Warn:counterClint:accumulateCounter:error:key");
        return;
    }

    // handle url path
    const urlPath = `${apiUrl}/${encodeURI(key)}`;

    // fetch data to counter server
    fetch(urlPath)
        .then((res) => res.json())
        .then((jsonRes) => jsonRes.status)
        .catch((err) =>
            console.error(
                "Error:counterClint:accumulateCounter:fetch:",
                err.message
            )
        );
};

module.exports = accumulateCounter;
