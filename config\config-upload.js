module.exports = {
    // port: 4321,
    UPLOAD_FIELD: {
        // image: {
        //     name: "image",
        //     maxSize: "20000000", // bytes, upload size limit per file, 20MB
        //     maxCount: 20, // upload files count limit
        //     minDpi: 300,
        //     mineTypePrefix: "image",
        //     ACCEPTABLE_EXTENSIONS: ["jpg", "jpeg", "png", "tiff"],
        //     dir: {
        //         default: "hkbdb/upload/image",
        //         single: "hkbdb/upload/image/single",
        //         batch: "hkbdb/upload/image/batch",
        //     },
        // },
        file: {
            name: "file",
            maxSize: "50000000", // bytes, upload size limit per file, 50MB
            maxCount: 5, // upload files count limit
            mineTypePrefix: "application",
            ACCEPTABLE_EXTENSIONS: [
                "vnd.ms-excel",
                "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ],
            dir: {
                default: "upload/file",
                nmtlImport: "nmtlImport",
            },
        },
    },
};
