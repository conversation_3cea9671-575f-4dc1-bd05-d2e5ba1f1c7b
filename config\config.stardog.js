const configStardog = {};

/*  URL backup
    http://localhost:3000
    http://localhost:5820
    http://*************:5820
    http://nmtl-db.daoyidh.com:5820
 */
if (process.env.NODE_ENV === "production") {
    // configStardog.db = process.env.STARDOG_DB;
    // configStardog.url = process.env.STARDOG_URL;
    // configStardog.user = process.env.STARDOG_USER;
    // configStardog.password = process.env.STARDOG_PASS;
    const {
        db,
        url,
        user,
        password,
    } = require("/opt/private-config/nmtl-db.json");
    configStardog.db = db;
    configStardog.url = url;
    configStardog.user = user;
    configStardog.password = password;
} else {
    configStardog.db = "nmtl";
    configStardog.url = "http://localhost:5820";
    configStardog.user = "admin";
    configStardog.password = "admin";
}

console.log("[ENV] STARDOG_DB", configStardog.db);
console.log("[ENV] STARDOG_URL", configStardog.url);
// console.log('[ENV] STARDOG_USER', configStardog.user);
// console.log('[ENV] STARDOG_PASS', configStardog.password);

module.exports = configStardog;
