const config = {};

// project prefix
config.PRJ_PREFIX = "tltc_";

// System definition
config.ALLOWED_DOMAIN = ".daoyidh.com";
config.ALLOWED_GOV_DOMAIN = ".nmtl.gov.tw";
config.ALLOWED_ORIGINS = [
    "http://localhost:9999",
    "http://0.0.0.0:9999",
    "http://localhost:7776",
    "http://0.0.0.0:7776",
    "http://0.0.0.0:7775",
    "http://0.0.0.0:7773",
    "http://localhost:7766",
    "http://0.0.0.0:7766",
    "http://0.0.0.0:7767",
    "http://localhost:3030",
    "http://localhost:3000",
    "http://0.0.0.0:3030",
    "http://0.0.0.0:3000",
    "http://localhost:7780",
];
config.ALLOWED_DL_IPS = [
    "**************",
    "***************",
    "*************",
    "**************",
    "**************",
];

config.REDUNDANT_PREFIX = "_";

if (process.env.NODE_ENV === "production") {
    if (process.env.ALLOWED_ORIGINS) {
        const origins = process.env.ALLOWED_ORIGINS.split(",");
        Object.assign(config.ALLOWED_ORIGINS, origins);
    }
}

console.log(`[ENV] ALLOWED_ORIGINS: ${config.ALLOWED_ORIGINS}`);

// REST API: CRUD
// config.READ_GENERAL = "/:lang/general/:ver";
// ERROR Codes
config.ERROR_NO_PAYLOAD = { error: "Payload is required." };
config.ERROR_NO_PARAM_VER = { error: "No ver parameter." };
config.ERROR_NO_CHANGE = { error: "Data are the same." };
config.ERROR_LACK_OF_PARAMS = { error: "Lack of parameters:" };
config.ERROR_WRONG_PARAMS = { error: "Wrong parameters:" };
config.ERROR_RESPONSE = { error: "Error response." };
config.ERROR_API_METHOD = { error: "API does not exist." };
config.ERROR_API_VER_NOT_EXIST = { error: "This API version does not exist." };
config.ERROR_API_NOT_EXIST = { error: "API or version does not exist." };
config.ERROR_GRAPH_DIFFERENT = { error: "The graphs are different." };
config.ERROR_GRAPH_REQUIRED = { error: "The graph parameter is required." };
config.ERROR_NOT_ALLOWED = { error: "It is not allowed." };
config.ERROR_FORBIDDEN_HTML = '<h1 style="text-align:center">Forbidden</h1>';
config.RESPONSE_OK = 200;
config.RESPONSE_BAD_REQUEST = 400;
// Parameter define
config.DEFAULT_MAX_LIMIT = 10;
config.DEFAULT_OFFSET = 0;

// For feature control
config.FEATURE_STORE_LIST = { stardog: 1, fuseki: 2 };
// 1, 2
config.FEATURE_STORE = 2;
config.FEATURE_STORE_WORDS = 2;

// 在 API 進行遠端 Fuseki 帳密的填入
config.SERVICE_NMTLWORDS = "SERVICE_NMTLWORDS";

// 在 Create 的時候, 如果沒有帶 graph 的預設值
config.DEFAULT_CREATE_GRAPH = "default";

config.STRING_SEPARATOR = "\n";

config.fileType = {
    image: "image",
    docs: "docs",
};

module.exports = config;
