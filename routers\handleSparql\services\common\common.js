const { namespace, graphIri, baseIri, prj<PERSON>ri } = require("../sparql/rdf");

const base64url = require("base64url");

const TEXT_PREFIX = "_text";
const LOGIC = { and: "&&", or: "||", not: "&& !" };
const DEFAULT_KEY = "default";
const langMap = { en: "en", "zh-tw": "zh", [DEFAULT_KEY]: "zh" };
// ISO 639-1
// https://zh.m.wikipedia.org/zh-tw/ISO_639-1
const AllowedLang = [
    "en",
    "zh",
    "zh-tw",
    "jp",
    "ja",
    "fr",
    "gu",
    "cs",
    "de",
    "es",
    "it",
    "ta",
    "tr",
    "sv",
    "ko",
    "vi",
    "hu",
    "th",
    "am",
    "bn",
    "nl",
    "ca",
    "te",
    "sl",
    "lb",
    "no",
    "rw",
    "ku",
    "mn",
    "ro",
    "pl",
    "la",
    "ru",
    "ms",
    "su",
    "hr",
    "sq",
    "sr",
    "ar",
    "uz",
];

const getFilter = (repType, keywordArr) => {
    let keywordStr = "";
    const filterStr =
        "(REGEX(?title, '''[keyword]''') || REGEX(?desc, '''[keyword]'''))";
    const repQueryBox = new RegExp(`\\[keyword\\]`, "g");

    for (let idx = 0; idx < keywordArr.length; idx = idx + 1) {
        const keyword = keywordArr[idx].slice(0, 1);
        const types = keywordArr[idx].slice(1, -1);
        const logic = keywordArr[idx].slice(-1);

        // keyword 為空字串，不搜尋
        if (keyword.length === 1 && keyword[0].length === 0) {
            continue;
        }

        // 取代搜尋字串
        const repKeyword = filterStr.replace(repQueryBox, keyword);
        if (idx === 0 && types.indexOf(repType) > -1) {
            keywordStr += repKeyword;
        } else if (types.indexOf(repType) > -1) {
            const logicStr = LOGIC.hasOwnProperty(logic) ? LOGIC[logic] : "&&";

            if (keywordStr.length > 0) {
                keywordStr += `${logicStr} ${repKeyword}`;
            } else {
                keywordStr += repKeyword;
            }
        }
    }
    return keywordStr.length > 0 ? `FILTER(${keywordStr}) .` : "";
};

const setLangauge = (queryStr, language) => {
    const repQueryBox = new RegExp(`\\[lang\\]`, "g");
    return queryStr.replace(repQueryBox, language);
};

const getFilter2 = (repType, filterObj) => {
    let keywordStr = "";
    const filterStr =
        "(REGEX(?title, '''[keyword]''') || REGEX(?desc, '''[keyword]'''))";
    const filterStrNot =
        "!(REGEX(?title, '''[keyword]''') && !REGEX(?desc, '''[keyword]'''))";

    if (typeof filterObj === "object") {
        const { and, or, not } = filterObj;
        // 取代搜尋字串
        if (and && repType === "and") {
            keywordStr += and;
        } else if (not && repType === "not") {
            keywordStr += not;
        } else if (or && repType === "or") {
            keywordStr += or;
        }
        return keywordStr;
    } else {
        // 取代搜尋字串
        if (repType === "and") {
            keywordStr += filterStr;
        } else if (repType === "not") {
            keywordStr += filterStrNot;
        }
        return keywordStr.length > 0 ? `FILTER(${keywordStr}) .` : "";
    }
};

exports.replaceQueryParams = (query, paramObj, key, language) => {
    if (typeof query === "object") {
        // multiple search
        if (
            query.hasOwnProperty("advancedSearch") &&
            query.hasOwnProperty("query")
        ) {
            let queryStr = "";

            const keyword1 = paramObj.hasOwnProperty("keyword1")
                ? paramObj["keyword1"]
                : "";
            const keyword2 = paramObj.hasOwnProperty("keyword2")
                ? paramObj["keyword2"]
                : "";
            const keyword3 = paramObj.hasOwnProperty("keyword3")
                ? paramObj["keyword3"]
                : "";

            const keyword1Arr = keyword1.split(",");
            const keyword2Arr = keyword2.split(",");
            const keyword3Arr = keyword3.split(",");

            Object.keys(query).forEach((repType) => {
                if (repType === "advancedSearch" || repType === "query") {
                    return;
                }
                let typeQryStr = query[repType];

                const filters = getFilter(repType, [
                    keyword1Arr,
                    keyword2Arr,
                    keyword3Arr,
                ]);

                if (filters.length > 0) {
                    typeQryStr += filters;
                    if (queryStr.length > 0) {
                        queryStr = `${queryStr} UNION {${typeQryStr}}`;
                    } else {
                        queryStr = `{${typeQryStr}}`;
                    }
                }
            });
            const repQueryBox = new RegExp(`\\[advancedSearch\\]`, "g");
            return setLangauge(
                query.query.replace(repQueryBox, queryStr),
                language
            );
        }

        /** ==== query.hasOwnProperty("query") ==== start ==== */
        // multiple section
        if (query.hasOwnProperty("query")) {
            let queryBoxStr = query.query;

            const prefix = query.hasOwnProperty("prefix")
                ? query["prefix"]
                : `${prjIri()}:`;
            // keys of query; ['query','perSec','artSec',...]
            const queryKeys = Object.keys(query);

            Object.keys(paramObj).forEach((param) => {
                if (param === "selection" || param === "selections") {
                    if (paramObj[param].length > 0) {
                        // selections 的格式：
                        // selections=perSec_and,pubSec_and,artSec_and
                        // selections=perSec,pubSec,artSec
                        const selections = paramObj[param].split(",");

                        selections.forEach((sec) => {
                            const secSplit = sec.split("_");
                            let secStr, secOperator;
                            if (secSplit.length >= 2) {
                                secStr = secSplit[0];
                                secOperator = secSplit[1];
                            } else {
                                secStr = secSplit[0];
                            }
                            const secStrIndexOfQueryKeys = queryKeys.findIndex(
                                (key) => key === secStr
                            );
                            if (secStrIndexOfQueryKeys > -1) {
                                queryKeys.splice(secStrIndexOfQueryKeys, 1);
                            }

                            if (!(secStr in query)) return;
                            const querySec = query[secStr];
                            const repQueryBox = new RegExp(
                                `\\[${secStr}\\]`,
                                "g"
                            );
                            if (typeof querySec === "string") {
                                queryBoxStr = queryBoxStr.replace(
                                    repQueryBox,
                                    querySec
                                );
                            } else if (
                                typeof querySec === "object" &&
                                secOperator &&
                                "advancedSearch" in querySec
                            ) {
                                let typeQuery = querySec.query;
                                const advanceSearchBox = new RegExp(
                                    `\\[advancedSearch\\]`,
                                    "g"
                                );
                                const filters = getFilter2(
                                    secOperator,
                                    querySec.filter
                                );
                                // 先把 querySec.query 中的 [advancedSearch] 置換成 filter
                                typeQuery = typeQuery.replace(
                                    advanceSearchBox,
                                    filters
                                );
                                queryBoxStr = queryBoxStr.replace(
                                    repQueryBox,
                                    typeQuery
                                );
                            }
                        });
                    }
                }

                // replace the boxes with parameters
                if (query.hasOwnProperty(param)) {
                    if (
                        paramObj[param].length > 0 &&
                        paramObj[param] !== "all"
                    ) {
                        const secStrIndexOfQueryKeys = queryKeys.findIndex(
                            (key) => key === param
                        );
                        if (secStrIndexOfQueryKeys > -1) {
                            queryKeys.splice(secStrIndexOfQueryKeys, 1);
                        }

                        // replace the box
                        const repParam = new RegExp(`\\[${param}\\]`, "g");
                        queryBoxStr = queryBoxStr.replace(
                            repParam,
                            query[param]
                        );

                        // replace the sub-ids
                        let repValues = "";
                        const ids = paramObj[param].split(",");
                        let idsStr = "";
                        ids.forEach((id) => {
                            if (prefix === "") {
                                idsStr += `'''${id}''' `;
                            } else {
                                // 此寫法在某些 id 會報錯, e.g. hkbdb:ORG香港浸會學院(香港浸會大學)
                                // idsStr += `hkbdb:${id} `;

                                // 改成此寫法
                                idsStr += `<${namespace.base}${id}> `;
                            }
                        });
                        if (param.endsWith(TEXT_PREFIX)) {
                            repValues += `(?_sid ?_score ?${param}) text:query (rdfs:label ${idsStr}) .`;
                        } else {
                            repValues += `VALUES ?${param} { ${idsStr} } .`;
                        }
                        queryBoxStr = queryBoxStr.replace(repParam, repValues);
                    }
                }
            });

            // replace the non-selected sectionBox#
            // handle non-selected secBox
            if (queryKeys.length > 0) {
                queryKeys.forEach((secKey) => {
                    const repQueryBox = new RegExp(`\\[${secKey}\\]`, "g");
                    queryBoxStr = queryBoxStr.replace(repQueryBox, "");
                });
            }

            Object.keys(paramObj).forEach((param) => {
                const repParam = new RegExp(`\\[${param}\\]`, "g");
                if (param === "id") {
                    if (paramObj[param].length > 0) {
                        const repId = paramObj[param];
                        queryBoxStr = queryBoxStr.replace(repParam, repId);
                    }
                } else if (param === "ids") {
                    let repValues = "";
                    if (paramObj[param].length > 0) {
                        const ids = paramObj[param].split(",");
                        let idsStr = "";
                        ids.forEach((id) => {
                            if (prefix === "") {
                                idsStr += `'''${id}''' `;
                            } else {
                                idsStr += `${prefix}${id} `;
                            }
                        });
                        repValues += `VALUES ?${key} { ${idsStr} } .`;
                    }
                    queryBoxStr = queryBoxStr.replace(repParam, repValues);
                } else {
                    if (paramObj[param].length > 0) {
                        const repVal = paramObj[param];
                        queryBoxStr = queryBoxStr.replace(repParam, repVal);
                    }
                }
            });

            // special case:  FILTER(REGEX(?title, '''[國国][家]''')) .
            // 不需要再 replace "[]"
            return setLangauge(queryBoxStr, language);

            // replace the non-selected sectionBox#
            // const leftBox = new RegExp(`\\[.*?\\]`, "g");
            // return setLangauge(queryBoxStr.replace(leftBox, ""), language);

            /** ==== query.hasOwnProperty("query") ==== end ==== */
        }
    }

    let resQuery = query;
    Object.keys(paramObj).forEach((param) => {
        const repParam = new RegExp(`\\[${param}\\]`, "g");

        if (param === "dataset") {
            const graphs = paramObj[param].split(",");
            let repGraphs = "";

            if (graphs.indexOf("all") >= 0) {
                repGraphs = "";
            } else {
                let sameTerm = "";
                graphs.forEach((g, idx) => {
                    if (idx === 0) {
                        sameTerm += `SAMETERM(?graph, ${graphIri(g)})`;
                        return;
                    }
                    sameTerm += ` || SAMETERM(?graph, ${graphIri(g)}) `;
                });
                repGraphs += `FILTER(${sameTerm}) .`;
            }
            resQuery = resQuery.replace(repParam, repGraphs);
        } else if (param === "ids") {
            let repValues = "";
            if (paramObj[param].length > 0) {
                const ids = paramObj[param].split(",");
                let idsStr = "";
                ids.forEach((id) => {
                    // 此寫法在某些 id 會報錯, e.g. hkbdb:ORG香港浸會學院(香港浸會大學)
                    // idsStr += `hkbdb:${id} `;

                    // 改成此寫法
                    idsStr += `<${namespace.base}${id}> `;
                });
                repValues += `VALUES ?${key} { ${idsStr} } .`;
            }
            resQuery = resQuery.replace(repParam, repValues);
        } else if (
            param === "personIds" ||
            param === "locationIds" ||
            param === "orgIds" ||
            param === "valueIds"
        ) {
            let repValues = "";
            if (paramObj[param].length > 0) {
                const ids = paramObj[param].split(",");
                let idsStr = "";
                ids.forEach((id) => {
                    idsStr += `${baseIri(id)} `;
                });
                repValues += idsStr;
            }
            resQuery = resQuery.replace(repParam, repValues);
        } else {
            if (param === "limit" || param === "offset") {
                return;
            }
            resQuery = resQuery.replace(repParam, paramObj[param]);
        }
    });
    return setLangauge(resQuery, language);
};

exports.isObjectEqual = (value, other) => {
    // Get the value type
    let type = Object.prototype.toString.call(value);

    // If the two objects are not the same type, return false
    if (type !== Object.prototype.toString.call(other)) return false;

    // If items are not an object or array, return false
    if (["[object Array]", "[object Object]"].indexOf(type) < 0) return false;

    // Compare the length of the length of the two items
    let valueLen =
        type === "[object Array]" ? value.length : Object.keys(value).length;
    let otherLen =
        type === "[object Array]" ? other.length : Object.keys(other).length;
    if (valueLen !== otherLen) return false;

    // Compare two items
    let compare = function (item1, item2) {
        // Get the object type
        let itemType = Object.prototype.toString.call(item1);

        // If an object or array, compare recursively
        if (["[object Array]", "[object Object]"].indexOf(itemType) >= 0) {
            if (!exports.isObjectEqual(item1, item2)) return false;
        }

        // Otherwise, do a simple comparison
        else {
            // If the two items are not the same type, return false
            if (itemType !== Object.prototype.toString.call(item2))
                return false;

            // Else if it's a function, convert to a string and compare
            // Otherwise, just compare
            if (itemType === "[object Function]") {
                if (item1.toString() !== item2.toString()) return false;
            } else {
                if (item1 !== item2) return false;
            }
        }
    };

    // Compare properties
    if (type === "[object Array]") {
        for (let i = 0; i < valueLen; i++) {
            if (compare(value[i], other[i]) === false) return false;
        }
    } else {
        for (let key in value) {
            if (value.hasOwnProperty(key)) {
                if (compare(value[key], other[key]) === false) return false;
            }
        }
    }

    // If nothing failed, return true
    return true;
};

exports.isObject = (value) => {
    if (value && Array.isArray(value)) {
        // not object
        return false;
    }
    return typeof value === "object" && value !== null;
};

// SubValue: [{}, {}]
exports.isSubValue = (value) => {
    if (value && Array.isArray(value)) {
        return !!(value[0] && typeof value[0] === "object");
    }
    return typeof value === "object" && value !== null;
};

exports.isValidEntry = (entry) => {
    return (
        typeof entry === "object" &&
        entry !== null &&
        Object.keys(entry).indexOf("srcId") > -1 &&
        Object.keys(entry).indexOf("value") > -1 &&
        Object.keys(entry).indexOf("classType") > -1
    );
};

exports.getYMD = (date) => {
    if (!date.includes("-")) {
        return [date, "", ""];
    }
    return date.split("-");
};

exports.paddingLeft = (str, length) => {
    if (str.length >= length) return str;
    else return this.paddingLeft("0" + str, length);
};

exports.convertToArray = (values) => {
    let resArray = [];
    if (!values) {
        return resArray;
    }

    if (Array.isArray(values)) {
        resArray = values;
    } else {
        resArray = values.split("/");
    }
    return resArray;
};

exports.map2SparqlLang = (lang) => {
    let resLang = langMap[DEFAULT_KEY];
    if (langMap.hasOwnProperty(lang)) {
        resLang = langMap[lang];
    }
    return resLang;
};

const excDecoQueryParams = ["limit", "offset"];

exports.bs64DecodeQueryStr = (queryStrObj) => {
    Object.keys(queryStrObj).forEach((param) => {
        if (excDecoQueryParams.indexOf(param) < 0) {
            // Since "+" is reserved parameters, so it will not be in the query parameters.
            // We could map " " to "+" back.
            const correctedQry = queryStrObj[param].replace(" ", "+");
            queryStrObj[param] = base64url.decode(correctedQry);
        }
    });
    return queryStrObj;
};

const isEmpty = (obj) => Object.keys(obj || {}).length === 0;

const replaceIllegalChar = (name) => {
    if (isEmpty(name)) {
        return "";
    }
    // bad_chars_list convert list
    const badCharsChange = {
        " ": "_",
        "^": "_",
        // "\\\\": "_",
        "|": "_",
        "<": "_",
        ">": "_",
        "{": "_",
        "}": "_",
        '"': "_",
        "`": "_",
    };
    if (name.length > 0) name = name.trim();
    // # remove bad_chars
    Object.keys(badCharsChange).forEach((bc) => {
        const cvtChar = badCharsChange[bc];
        if (name && name.indexOf(bc) >= 0) {
            name = `${name || ""}`.replace(new RegExp(bc, "g"), cvtChar);
        }
    });

    return name.replace(/\\/gm, "_");
};

const illegalParamCheck = ["name"];

// replace illegal IRI character in query string
exports.replIllegalIRIChar = (queryStrObj) => {
    if (queryStrObj === undefined || queryStrObj === null) return queryStrObj;

    // String type
    if (typeof queryStrObj === "string") {
        return replaceIllegalChar(queryStrObj);
    }

    // Object type
    Object.keys(queryStrObj).forEach((param) => {
        if (illegalParamCheck.indexOf(param) >= 0) {
            queryStrObj[param] = replaceIllegalChar(queryStrObj[param]);
        }
    });
    return queryStrObj;
};

exports.decodePersonId = (entry) => {
    if (!entry.hasOwnProperty("srcId")) {
        return null;
    }

    const { srcId } = entry;
    if (isEmpty(srcId)) {
        return null;
    }
    const prefix = `${srcId}`.slice(0, 3);
    const person = `${srcId}`.slice(3);
    const oriPersonId = `${prefix}${base64url.decode(person)}`;
    if (isEmpty(oriPersonId)) {
        return null;
    }
    return {
        ...entry,
        srcId: oriPersonId,
    };
};

exports.decodeId = (entry, target = "srcId") => {
    if (!entry.hasOwnProperty(target)) {
        return entry;
    }

    const id = entry[target];
    if (isEmpty(id)) {
        return entry;
    }
    const prefix = `${id}`.slice(0, 3);
    const content = `${id}`.slice(3);
    const oriId = `${prefix}${base64url.decode(content)}`;
    if (isEmpty(oriId)) {
        return entry;
    }
    return {
        ...entry,
        [target]: oriId,
    };
};

exports.getLanguageTag = (label) => {
    // 沒有包含語系，給 default 語系
    if (label.length <= 3 || label.slice(-3, -2) !== "@") {
        return [label, ""];
    }

    const oriStr = label.slice(0, label.length - 3);
    const langTag = label.slice(-2);
    // 語系不支援
    if (AllowedLang.indexOf(langTag) < 0) {
        return [label, ""];
    }

    return [oriStr, `@${langTag}`];
};

exports.getStrokeLanguageTag = (label) => {
    let zhLabel = label;
    // 可能是 Array
    if (Array.isArray(label)) {
        // 只找 zh
        zhLabel = label.filter((l) => l && l.endsWith("@zh"))[0];

        if (!zhLabel) {
            return ["", ""];
        }
    }
    // 沒有包含語系，給 default 語系
    if (zhLabel.length <= 3 || zhLabel.slice(-3, -2) !== "@") {
        return [zhLabel, ""];
    }

    const oriStr = zhLabel.slice(0, zhLabel.length - 3);
    const langTag = zhLabel.slice(-2);
    // 語系不支援
    if (AllowedLang.indexOf(langTag) < 0) {
        return [zhLabel, ""];
    }

    return [oriStr, `@${langTag}`];
};

exports.insertMultipleLabel = (bindId, labels) => {
    let insert = "";
    if (typeof labels === "string") {
        const [pureLabel, langTag] = this.getLanguageTag(labels);
        if (pureLabel && pureLabel !== "") {
            insert += `?${bindId} rdfs:label '''${pureLabel}'''${langTag} .`;
        }
    } else if (Array.isArray(labels)) {
        // 多個 label, 且帶語系
        labels.forEach((cl) => {
            const [pureLabel, langTag] = this.getLanguageTag(cl);
            if (pureLabel && pureLabel !== "") {
                insert += `?${bindId} rdfs:label '''${pureLabel}'''${langTag} .`;
            }
        });
    }
    return insert;
};
