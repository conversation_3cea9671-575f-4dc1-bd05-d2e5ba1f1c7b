const { splitFileNameExt } = require("../commons/common");
const {
    getNmtlFolderFiles,
} = require("../routers/handleImport/helpers/fileHelper");
const { imagePathDist, filePath } = require("./config.fileServer");
const { fileType } = require("./config.js");

const GRAPH = {
    tltc: "tltc",
};

// import file extension
const EXTENSION = {
    xlsx: "xlsx",
};

// excelColKey後續改用firebase config headers-id 設定值
const excelColKey = {
    srcId: "srcId",
    label_Person: "label_Person",
    hasTranslationLanguage: "hasTranslationLanguage",
    authorOrTranslator: "authorOrTranslator",
    birthName: "birthName",
    penName: "penName",
    otherName: "otherName",
    introduction: "introduction",
    externalLinks: "externalLinks",
    imageURL_hasURL: "imageURL_hasURL",
    hasCopyrightStatus_hasURL: "hasCopyrightStatus_hasURL",
    comment: "comment",
    pictureDisplay: "pictureDisplay",
    isTranslationBookOf: "isTranslationBookOf",
    label_Publication: "label_Publication",
    hasAuthor: "hasAuthor",
    // srcId: "srcId",
    hasLanguageOfWorkOrName: "hasLanguageOfWorkOrName",
    translationBookName: "translationBookName",
    lastModified: "lastModified",
    authorName: "authorName",
    hasTranslator: "hasTranslator",
    translatorName: "translatorName",
    hasEditor: "hasEditor",
    hasPublisher: "hasPublisher",
    hasInceptionDate: "hasInceptionDate",
    srcId_hasPlaceOfPublication: "srcId_hasPlaceOfPublication",
    LiteraryGenre: "LiteraryGenre",
    totalPage: "totalPage",
    ISBN: "ISBN",
    references: "references",
    // introduction: "introduction",
    tableOfContents: "tableOfContents",
    fileAvailableAt: "fileAvailableAt",
    hasFullWorkCopyright: "hasFullWorkCopyright",
    // imageURL_hasURL: "imageURL_hasURL",
    // hasCopyrightStatus_hasURL: "hasCopyrightStatus_hasURL",
    // comment: "comment",
    lift: "lift",
    peak: "peak",
    friendlyLink: "friendlyLink",
    geoLatitude_hasPlaceOfPublication: "geoLatitude_hasPlaceOfPublication",
    geoLongitude_hasPlaceOfPublication: "geoLongitude_hasPlaceOfPublication",
};

// 每個 pattern 設定:
// - excel 要有哪些欄位
const importConfig = {
    GRAPH: GRAPH,
    EXTENSION: EXTENSION,
    excelColKey,
    pattern: {
        [GRAPH.tltc]: {
            graph: GRAPH.tltc,
            extension: EXTENSION.xlsx,
            /** 需要找到instance id 的欄位 */
            specialCol: [
                excelColKey.label_Publication,
                excelColKey.hasAuthor,
                excelColKey.hasEditor,
                excelColKey.authorName,
                excelColKey.hasTranslator,
                excelColKey.hasPublisher,
                excelColKey.srcId_hasPlaceOfPublication,
            ],
            /** {
             *    excelColKey: 匯入文件Header顯示的ID,
             *    method: 轉query的對應function, return {[real property name]: [value]}
             *    realProp: 會對應到DB ontology property欄位，對應欄位需要再確認
             *  }
             * */
            fileCols: [
                {
                    excelColKey: excelColKey.srcId,
                    method: () => {},
                    realProp: false,
                },
                {
                    excelColKey: excelColKey.label_Person,
                    method: (value) => {
                        return { label: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasTranslationLanguage,
                    method: (value, langList) => {
                        const findID = langList.data.find(
                            (el) => el.label === value[0]
                        );
                        if (findID?.id) {
                            return {
                                hasTranslationLanguage: [findID?.id],
                            };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.authorOrTranslator,
                    method: () => {},
                    realProp: false,
                },
                {
                    excelColKey: excelColKey.birthName,
                    method: (value) => {
                        return { birthName: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.penName,
                    method: (value) => {
                        return { penName: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.otherName,
                    method: (value) => {
                        return { otherName: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.introduction,
                    method: (value) => {
                        return { introduction: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.externalLinks,
                    method: (value) => {
                        return { externalLinks: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.imageURL_hasURL,
                    method: (value, pattern, imgPath, sheetName) => {
                        // get file name list
                        const folderPath = `${imagePathDist[sheetName]}/${pattern}`;
                        const type = fileType.image;
                        const fsFileList =
                            getNmtlFolderFiles(folderPath, type)?.data || [];

                        const cellFileName = splitFileNameExt(value[0]);

                        // 判斷圖片是否已經存在相對路徑檔案中
                        const newFileName =
                            fsFileList.find((str) => {
                                return str.indexOf(cellFileName) === 0;
                            }) || value[0];

                        return {
                            imagePath: `${filePath.imageUploadPath}/${imgPath}/${pattern}`,
                            imageName: [newFileName],
                        };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasCopyrightStatus_hasURL,
                    method: (value, copyrightList) => {
                        const findLabel = copyrightList.data.find(
                            ({ label }) => {
                                return label === value[0];
                            }
                        );
                        return {
                            hasCopyrightStatus: findLabel ? [findLabel.id] : [],
                        };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.comment,
                    method: (value) => {
                        return { comment: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.pictureDisplay,
                    method: (value) => {
                        return { pictureDisplay: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.isTranslationBookOf,
                    method: () => {},
                    realProp: false,
                },
                {
                    excelColKey: excelColKey.label_Publication,
                    method: (value, pubId = "") => {
                        // 原文書 -> 翻譯書
                        if (pubId) {
                            return { isTranslationBookOf: [pubId] };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasAuthor,
                    method: (value, allId = []) => {
                        if (allId.length > 0) {
                            return { hasAuthor: allId };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasEditor,
                    method: (value, allId = []) => {
                        if (allId.length > 0) {
                            return { hasEditor: allId };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasTranslator,
                    method: (value, allId = []) => {
                        if (allId.length > 0) {
                            return { hasTranslator: allId };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasPublisher,
                    method: (value, allId = []) => {
                        if (allId.length > 0) {
                            return { hasPublisher: allId };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.translationBookName,
                    method: (value) => {
                        if (value.length > 0) {
                            return { label: `${value}@zh` };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.authorName,
                    method: (value) => {
                        const tmpArr = [];

                        value.map((str) => {
                            const translatorEnName = str.split('_')[2];
                            const translatorChName = str.split('_')[1];

                            if(translatorEnName) tmpArr.push(translatorEnName)
                            if(translatorChName) tmpArr.push(translatorChName)
                        });

                        if (value.length > 0) {
                            return { authorName: tmpArr };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.translatorName,
                    method: (value) => {
                        const tmpArr = [];

                        value.map((str) => {
                            const translatorEnName = str.split('_')[2];
                            const translatorChName = str.split('_')[1];

                            if(translatorEnName) tmpArr.push(translatorEnName)
                            if(translatorChName) tmpArr.push(translatorChName)
                        });
                        if (value.length > 0) {
                            return { translatorName: tmpArr };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasInceptionDate,
                    method: (value) => {
                        if (value.length > 0) {
                            return {
                                hasInceptionDate: value.map(
                                    (val) => `${val}`
                                ),
                            };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.srcId_hasPlaceOfPublication,
                    method: (value, locId) => {
                        if (locId) {
                            return {
                                hasPlaceOfPublication: [locId],
                            };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.LiteraryGenre,
                    method: (value, litGeneryList) => {
                        const findID = litGeneryList.data.find(
                            (el) => el.label === value[0]
                        );
                        if (findID?.id) {
                            return {
                                LiteraryGenre: [findID?.id],
                            };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasLanguageOfWorkOrName,
                    method: (value, langList) => {
                        const findID = langList.data.find(
                            (el) => el.label === value[0]
                        );
                        if (findID?.id) {
                            return {
                                hasLanguageOfWorkOrName: [findID?.id],
                            };
                        }
                        return {};
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.totalPage,
                    method: (value) => {
                        return { totalPage: `${value}` };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.ISBN,
                    method: (value) => {
                        return { ISBN: `${value}` };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.references,
                    method: (value) => {
                        return { references: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.tableOfContents,
                    method: (value) => {
                        return { tableOfContents: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.fileAvailableAt,
                    method: (value, pattern) => {
                        // get file name list
                        const folderPath = `${pattern}`;
                        const type = fileType.docs;
                        const fsFileList =
                            getNmtlFolderFiles(folderPath, type)?.data || [];

                        const filePathPrefix = `${filePath.readFile}/${pattern}`;
                        const allFilePath = value.map((fileName) => {
                            const cellFileName = splitFileNameExt(fileName);

                            const newFileName =
                                fsFileList.find((str) => {
                                    return str.indexOf(cellFileName) === 0;
                                }) || fileName;

                            return `${filePathPrefix}/${newFileName}`;
                        });
                        return { fileAvailableAt: allFilePath };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.hasFullWorkCopyright,
                    method: (value, copyrightList) => {
                        const findLabel = copyrightList.data.find(
                            ({ label }) => {
                                return label === value[0];
                            }
                        );
                        return {
                            hasCopyrightStatus: findLabel ? [findLabel.id] : [],
                        };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.lift,
                    method: (value) => {
                        return { lift: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.peak,
                    method: (value) => {
                        return { peak: value };
                    },
                    realProp: true,
                },
                {
                    excelColKey: excelColKey.friendlyLink,
                    method: (value) => {
                        return { friendlyLink: value };
                    },
                    realProp: true,
                },
            ],
        },
    },
};

module.exports = importConfig;
