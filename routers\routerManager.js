const compress = require("compression");
// Counter
// const counterAop = require("./handleCounter/counterAop");
// Sparql
const sparqlCreate = require("./handleSparql/sparqlCreate");
const sparqlRead = require("./handleSparql/sparqlRead");
const sparqlReadMassive = require("./handleSparql/sparqlReadMassive");
const sparqlUpdate = require("./handleSparql/sparqlUpdate");
const sparqlDelete = require("./handleSparql/sparqlDelete");
// Mongodb
// const mongodbReadGeneral = require("./handleMongodb/mongodb-read-general");
// systemInfo
const systemInfo = require("./handleSystemInfo");
// database
const database = require("./handleDatabase");
// health
const health = require("./handleHealth");
// schedule
const handleSchedule = require("./handleSchedule");
// schedule update realtime database
const { handleUpdateRealTimeAll } = require("./handleUpdateRealTime");
//
// redis cache
const {
    redisGetCache,
    redisSetCache,
    redisGetMassiveCache,
    redisSetMassiveCache,
    redisFlushCache,
    redisDumpCache,
} = require("./handleRedis/handleRedis");
const { publicIp } = require("./handlePublicIp/handlePublicIp");
const firebaseInfo = require("./handleFirebaseInfo/handleFirebaseInfo");
// Mail
const handleMail = require("./handleMail");
// Download Firestore
const dlFirestore = require("./handleDlFirestore");
// export
const handleExport = require("./handleExport");
// data import
const handleImport = require("./handleImport");
const { httpRestrictIp } = require("./handleRestrictDomain");

function router(app) {
    // Compress
    app.use(compress());
    // schedule (daily realtime database)
    app.post("/schedule/update/realTime/all", handleUpdateRealTimeAll);
    // schedule (daily counter)
    app.post("/schedule/:cron", handleSchedule);
    // Counter
    // deprecated.
    // app.get("/*", counterAop);
    // Mongodb
    // app.get(READ_GENERAL, mongodbReadGeneral);
    app.get("/health", health);
    // Download Firestore
    app.get("/dlFS", [httpRestrictIp], dlFirestore);
    // export
    app.use("/export", handleExport);
    // data import
    app.use("/import", handleImport);
    // mail
    app.post("/mail", handleMail);
    // Public Ip
    app.get("/publicIp", publicIp);
    // Server info
    app.get("/firebase/:info", firebaseInfo);
    // API cache
    app.get("/api/cache", redisDumpCache);
    app.get("/api/cache/flush", redisFlushCache);

    // database
    app.get("/:lang/database/:act", database);
    // Sparql
    app.post(
        "/:lang/post/*/:ver",
        redisGetMassiveCache,
        sparqlReadMassive,
        redisSetMassiveCache
    );
    app.post("/:lang/*/:ver", redisFlushCache, sparqlCreate);
    app.get("/:lang/*/:ver", redisGetCache, sparqlRead, redisSetCache);
    app.put("/:lang/*/:ver", redisFlushCache, sparqlUpdate);
    app.delete("/:lang/*/:ver", redisFlushCache, sparqlDelete);
    // systemInfo
    app.get("/:lang/systemInfo", systemInfo);
}

module.exports = router;
