// firebase
const _ = require("lodash");
const firebase = require("../../../config/config.firebase");
const { getFileNameAndKey } = require("./fileHelper");
const exportConfig = require("../../../config/config.export");

const rtdb = firebase.database();
const firebaseHelper = {};

firebaseHelper.rtDbGet = (path) => {
    const data = {};
    return rtdb
        .ref(path)
        .once("value")
        .then((snapshot) => {
            const snapVal = snapshot.val() || {};
            if (_.isEmpty(snapVal)) {
                return {};
            }
            if (typeof snapVal === "object") {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
                return data;
            }
            if (typeof snapshot.val() === "string") {
                throw new Error("firebase data not valid");
            }
        })
        .catch((err) => {
            return err;
        });
};

firebaseHelper.rtDbUpdate = (path, updateVal) => {
    if (!(path && updateVal)) return null;
    return firebase
        .database()
        .ref(path)
        .update(updateVal)
        .then((res) => {
            return res;
        })
        .catch((err) => {
            return err;
        });
};

// 更新 realtime database 的資訊: files 新增 file 資訊, register 新增 file 及 user 資訊
firebaseHelper.genRtDbSnapValRegister = ({
    data = {},
    user,
    fileName,
    fileKey,
    graph,
    sheet,
    splitNum,
}) => {
    const ts = Date.now();
    const { name, email, uid: userUid } = user || {};
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                requestTime: new Date(ts),
                createdTime: null,
                downloadUrl: null,
                requestUser: name,
                requestEmail: email,
                fileName,
                graph,
                sheet,
                splitNum,
            },
        },
        register: {
            ...(data.register || {}),
            [fileKey]: {
                [userUid]: {
                    email: email,
                    name: name,
                    emailRecord: {},
                    downloadRecord: {},
                },
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4DownloadFail = ({
    data = {},
    fileKey,
    errorMessage,
}) => {
    const ts = Date.now();
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...(data || {}).files[fileKey],
                error: "true",
                errorTime: new Date(ts),
                errorMessage: errorMessage,
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4DownloadReady = ({
    data = {},
    user,
    fileKey,
    downloadUrl,
}) => {
    const ts = Date.now();
    const { name, email, uid: userUid } = user || {};
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...(data || {}).files[fileKey],
                finishTime: new Date(ts),
                downloadUrl: downloadUrl,
            },
        },
        register: {
            ...((data || {}).register || {}),
            [fileKey]: {
                [userUid]: {
                    email: email,
                    name: name,
                    emailRecord: {
                        [ts]: "true",
                    },
                    downloadRecord: {},
                },
            },
        },
    };
};

firebaseHelper.registerNewfile = async (user, { graph, sheet, splitNum }) => {
    // realtime database specific path
    // rtDbPath: "/database/export";
    const rtDbPath = exportConfig.firebaseRtDb.readWritePath;

    // 先取得 realtime database 設定檔
    const rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
    // console.log("rtDbDataObj::", rtDbDataObj);

    // 取得 fileKey and fileName
    const { fileKey, fileName, zipFileName } = getFileNameAndKey();

    // console.log(
    //     "fileKey, fileName, zipFileName::",
    //     fileKey,
    //     fileName,
    //     zipFileName
    // );
    // 產生更新 realtime database 的資料: files 新增 file 資訊, register 新增 file 及 user 資訊
    const updateVal = firebaseHelper.genRtDbSnapValRegister({
        data: rtDbDataObj,
        user,
        fileName: zipFileName,
        fileKey,
        graph,
        sheet,
        splitNum,
    });
    // console.log("updateVal", updateVal);
    // 更新 realtime database 的資訊
    await firebaseHelper.rtDbUpdate(rtDbPath, updateVal);

    return { fileName, zipFileName, fileKey };
};

module.exports = firebaseHelper;
