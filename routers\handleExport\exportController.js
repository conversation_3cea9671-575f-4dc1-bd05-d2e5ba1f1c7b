const { BAD_REQUEST } = require("../../commons/httpStatusCode");
const firebaseHelper = require("./helpers/firebaseHelper");
const errorHelper = require("./helpers/errorHelper");
const exportMain = require("./exportMain");

const exportController = {
    exportFile: async (req, res, next) => {
        try {
            // req.body: {
            //     "user": {
            //         "email": "<EMAIL>",
            //           "name": "abcdefg",
            //           "uid": "asdfqwregafasdfasdfqwrwe"
            //     },
            //     "file": {
            //         "graph": ["twp", "tww"],
            //         "sheet": [
            //             "BasicInfo",
            //             "Publication"
            //         ],
            //         "splitNum": 1000
            //     }
            // }
            const { user, file } = req.body;
            const { graph, sheet } = file || {};

            if (!graph || !sheet) {
                res.json(BAD_REQUEST);
            }
            if (!Array.isArray(graph) || !Array.isArray(sheet)) {
                res.json(BAD_REQUEST);
            }

            // 先把要處理的資料放 RealTime database
            const { fileName, zipFileName, fileKey } =
                await firebaseHelper.registerNewfile(user, file);

            // (非同步)匯出流程
            exportMain({
                user,
                file,
                fileName,
                zipFileName,
                fileKey,
            });

            res.json({
                status: "success",
                message: "File is in exporting.",
            });
        } catch (err) {
            console.log("error:", err.message);
            let errObj = errorHelper.ERROR_CODE.DEFAULT;
            if (err.message in errorHelper.ERROR_CODE) {
                errObj = errorHelper.ERROR_CODE[err.message];
                console.log("errObj", errObj);
            }
            if (errObj) {
                res.status(errObj.code).json({
                    statusCode: errObj.code,
                    message: errObj.message,
                });
            }
        }
    },
};

module.exports = exportController;
