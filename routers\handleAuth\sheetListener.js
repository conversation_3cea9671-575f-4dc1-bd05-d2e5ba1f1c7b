const firebase = require("../../config/config.firebase");

const sheetListener = async (callback) => {
    const ref = firebase
        .firestore()
        .collection("setting")
        .doc("dataset")
        .collection("sheet");
    return (
        ref
            // .orderBy("seq", "asc")
            .get()
            .then((querySnapshot) => {
                const sheetObj = {};
                querySnapshot.forEach(
                    (doc) =>
                        (sheetObj[doc.id] = {
                            enable: doc.data()["enable"],
                            label: doc.data()["label"],
                            getClassList: doc.data()["getClassList"],
                            getTable2: doc.data()["getTable2"],
                            headers: doc.data()["headers"],
                        })
                );
                callback(sheetObj);
            })
    );
};

module.exports = sheetListener;
