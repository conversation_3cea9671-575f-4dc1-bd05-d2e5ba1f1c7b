let apiObj = null;
let apiHelper = {};
let AppNameNumber = process.env.PORT || "Local";

let sheetObj = null;

module.exports = {
    getAppName() {
        // 根據 Firebase，如果要執行多個 app，在 initializeApp 時給第二個參數
        const timestamp = Date.now();
        return `${timestamp}_${AppNameNumber}`;
    },
    // apiObj
    setApiObj(api) {
        apiObj = api;
    },
    getApiObj() {
        return apiObj;
    },
    isApiObjNull() {
        return apiObj === null;
    },
    getInvertRelation() {
        return apiObj["read"]["relation/invert/list"]["1.0"];
    },
    getProperties() {
        return apiObj["read"]["property/list"]["1.0"];
    },
    getUpdateLinks() {
        return apiObj["backend"]["update/delete"]["1.0"];
    },
    getReservedCurId() {
        return apiObj.required["curid"]["1.0"].query;
    },
    getMaxId() {
        return apiObj.required["class/maxid"]["1.0"].query;
    },
    isProduction() {
        return (
            !apiObj["api-config"].production ||
            apiObj["api-config"].production === "false"
        );
    },
    isDevelop() {
        return (
            !apiObj["api-config"].develop ||
            apiObj["api-config"].develop === "false"
        );
    },
    getToken() {
        return apiObj["api-config"].token;
    },
    getApi(api) {
        if (apiObj === null) {
            return null;
        }

        const prefixIdx = api.indexOf("/");
        if (prefixIdx < 0) {
            // default
            return null;
        }

        // 第一個斜線前面為文件目錄名稱
        // ex: backend/tlvmperiod/list/2.0
        // backend 為 api 目錄
        const apiDoc = api.slice(0, prefixIdx);
        // tlvmperiod/list/2.0
        const apiEle = api.slice(prefixIdx + 1);

        // 除了 api-config 以外，其它文件都是 API
        const ApiDocs = Object.keys(apiObj).filter((a) => a !== "api-config");

        // 在其它的 doc 找到
        if (ApiDocs.indexOf(apiDoc) > -1) {
            return apiObj[apiDoc][apiEle];
        }

        // default
        return null;
    },
    getMailApi(method, api) {
        if (apiObj === null) {
            return null;
        }
        return apiObj[method][api];
    },
    // apiHelper
    setApiHelper(helper) {
        apiHelper = helper;
    },
    getApiProperty() {
        if (apiHelper === null) {
            return null;
        }
        return apiHelper.property;
    },
    isGetRangeLabel(property) {
        if (
            ["CollectibleType", "MainSubject", "LiteraryGenre"].includes(
                property
            )
        ) {
            return [property];
        }
        // 這類 range 只需要取其 label
        if (apiHelper === null) {
            return null;
        }
        const ranges = apiHelper.property
            .filter((p) => p.type === "object")
            .filter((p) => p.property === property)
            .map((sp) => sp.range);

        // 去掉重覆的
        return [...new Set(ranges)];
    },
    getApiRelation() {
        if (apiHelper === null) {
            return null;
        }
        return apiHelper.relation;
    },
    getFullWorkAllowIps() {
        if (apiObj === null) {
            return null;
        }
        return apiObj["api-config"]["fullWorkAllowIps"];
    },
    // sheetObj
    setSheetObj(sObj) {
        sheetObj = sObj;
    },
    getSheetObj() {
        return sheetObj;
    },
    getSortedHeader(sheet) {
        return sheetObj[sheet].headers.sort((a, b) => a.seq - b.seq);
    },
    getSheetHeaderId(sheet) {
        if (!(sheet in sheetObj)) {
            return [];
        }
        return module.exports
            .getSortedHeader(sheet)
            .filter((a) => {
                if ("forCreate" in a) {
                    return a.forCreate !== "1";
                }
                return true;
            })
            .map((e) => e.id);
    },
    getSheetHeaderLabel(sheet) {
        if (!(sheet in sheetObj)) {
            return [];
        }
        return module.exports
            .getSortedHeader(sheet)
            .filter((a) => {
                if ("forCreate" in a) {
                    return a.forCreate !== "1";
                }
                return true;
            })
            .map((e) => e.label);
    },
};
