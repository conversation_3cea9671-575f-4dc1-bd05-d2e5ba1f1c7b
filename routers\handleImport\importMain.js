const fs = require("fs");

// config
const { STRING_SEPARATOR } = require("../../config/config");
const importConfig = require("../../config/config.import");
// helpers
const fileHelper = require("./helpers/fileHelper");
const errorHelper = require("./helpers/errorHelper");
const mailHelper = require("./helpers/mailHelper");
//
const { excelColKey } = importConfig;
const { getFilePath, getFinishFileName } = fileHelper;
const { emailToUser, mailMessage } = mailHelper;
const { imagePathDist } = require("../../config/config.fileServer");

// common
const {
    generic20,
} = require("../handleSparql/services/crud/createApis/lib/createGeneric");
const {
    getReservedNewId,
} = require("../handleSparql/services/common/getReservedNewId");
const { getImageUploadPath } = require("../handleAuth/dlFirebaseDb");

const {
    getCopyrightStatus,
    getDsPerOrgInfolist,
    getDsPublicationList,
    getLanguage,
    getLiteraryGenre,
    getLocList,
} = require("../../config/config.api");
const { classPrefix } = require("../handleSparql/services/sparql/classPrefix");
const {
    getListWithoutParams,
    getListWithParams,
    getLocIDObj,
    checkWrongList,
} = require("./helpers/checkDataHelper");
const { isEmpty } = require("lodash");

// 寫入主程序
const importMain = async (req, res) => {
    const { pattern } = req.params;
    const { sheetName } = req.query;
    const { userName, userEmail } = req.body;
    const user = { name: userName, email: userEmail };

    try {
        // 取得要處理的檔案路徑
        const filePath = getFilePath(req);
        // 取得 firebase config圖片儲存路徑
        const imgPathRef = await getImageUploadPath(pattern);

        // prepare check list data
        // copyright list
        const copyrightList = await getListWithoutParams(getCopyrightStatus);

        // per and org list
        let perOrgList = await getListWithParams(
            { ds: pattern },
            getDsPerOrgInfolist
        );

        // publication list
        let pubList = await getListWithParams(
            { ds: pattern, lang: "zh" },
            getDsPublicationList
        );

        // language list
        const langList = await getListWithParams({ lang: "zh" }, getLanguage);

        // litGeneryList
        const litGeneryList = await getListWithoutParams(getLiteraryGenre);

        // location list
        let locList = await getListWithParams({ ds: pattern }, getLocList);

        let rows = [];
        const sheetRows = await fileHelper.readExcelFile(filePath);
        if (sheetRows && sheetRows[0] && sheetRows[0].data) {
            rows = sheetRows[0].data;
        }

        if (!rows) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.FILE_ROWS_BLANK.name
            );
        }

        // 確認要執行何種 import pattern
        const patternSetting = importConfig.pattern[pattern];
        if (!patternSetting) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.PATTERN_NOT_EXIST.name
            );
        }
        const { fileCols, specialCol } = patternSetting;
        const processCol = fileCols.filter((el) => el.realProp);

        // 資料格式轉換
        const mapList = fileHelper.arrayToMapList(rows.slice(1), rows[0]);

        // 所有需要靠label找Id的欄位先確認，先把Id建立完成
        for (let rowData of mapList) {
            for (let key of Object.keys(rowData)) {
                const colKeyID = key.split(STRING_SEPARATOR)[1];
                if (specialCol.includes(colKeyID) && rowData[key]) {
                    const allValues = rowData[key].split(STRING_SEPARATOR);
                    switch (colKeyID) {
                        case excelColKey.label_Publication: {
                            for (let val of allValues) {
                                const findId = pubList.data.find(
                                    (el) => el.label === val
                                );

                                if (!findId?.id) {
                                    const evtType = "Publication";
                                    // create new entry
                                    const tmpEntry = {
                                        classType: evtType,
                                        graph: pattern,
                                        srcId: await getReservedNewId(evtType),
                                        value: {
                                            label: [`${val}@zh`],
                                        },
                                    };
                                    await generic20(tmpEntry, () => {});
                                }
                            }
                            break;
                        }
                        case excelColKey.hasTranslator:
                        case excelColKey.hasAuthor: {
                            for (let val of allValues) {
                                const findId = perOrgList.data.find(
                                    (el) => el.label === val
                                );
                                if (!findId?.id) {
                                    const type = val.split("@").pop();
                                    const evtType = classPrefix.find(
                                        ({ prefix }) => prefix === type
                                    )?.eventType;
                                    if (evtType) {
                                        // create new entry
                                        const perId = await getReservedNewId(
                                            evtType
                                        )

                                        const targetVal = val.split('_')[1]

                                        const tmpEntry = {
                                            classType: evtType,
                                            graph: pattern,
                                            srcId: perId,
                                            value: {
                                                label: `${targetVal.replace(
                                                    new RegExp(
                                                        `@${type}` + "$"
                                                    ),
                                                    ""
                                                )}`,
                                            },
                                        };
                                        await generic20(tmpEntry, () => {});
                                    }
                                }
                            }
                            break;
                        }
                        case excelColKey.hasPublisher:
                        case excelColKey.hasEditor: {
                            for (let val of allValues) {
                                const findId = perOrgList.data.find(
                                    (el) => el.label === val
                                );

                                if (!findId?.id) {
                                    const type = val.split("@").pop();
                                    const evtType = classPrefix.find(
                                        ({ prefix }) => prefix === type
                                    )?.eventType;
                                    if (evtType) {
                                        // create new entry
                                        const perId = await getReservedNewId(
                                            evtType
                                        )

                                        const tmpEntry = {
                                            classType: evtType,
                                            graph: pattern,
                                            srcId: perId,
                                            value: {
                                                label: `${val.replace(
                                                    new RegExp(
                                                        `@${type}` + "$"
                                                    ),
                                                    ""
                                                )}`,
                                            },
                                        };
                                        await generic20(tmpEntry, () => {});
                                    }
                                }
                            }
                            break;
                        }
                        case excelColKey.srcId_hasPlaceOfPublication: {
                            // getLocIDArr
                            const geoColKey = [
                                excelColKey.geoLatitude_hasPlaceOfPublication,
                                excelColKey.geoLongitude_hasPlaceOfPublication,
                            ].map((str) =>
                                Object.keys(rowData).find(
                                    (keyStr) =>
                                        keyStr.split(STRING_SEPARATOR)[1] ===
                                        str
                                )
                            );

                            const findId = getLocIDObj(
                                rowData,
                                key,
                                geoColKey,
                                locList
                            );

                            if (!findId?.id) {
                                const evtType = "Location";
                                // create new entry
                                const tmpEntry = {
                                    classType: evtType,
                                    graph: pattern,
                                    srcId: await getReservedNewId(evtType),
                                    value: {
                                        label: rowData[key],
                                        geoLatitude: rowData[geoColKey[0]],
                                        geoLongitude: rowData[geoColKey[1]],
                                    },
                                };
                                await generic20(tmpEntry, () => {});
                            }
                            break;
                        }
                        default:
                            break;
                    }
                }
            }
        }

        // update check list data again
        perOrgList = await getListWithParams(
            { ds: pattern },
            getDsPerOrgInfolist
        );
        pubList = await getListWithParams(
            { ds: pattern, lang: "zh" },
            getDsPublicationList
        );

        locList = await getListWithParams({ ds: pattern }, getLocList);

        // 檢查清單
        let checkList = [];

        const allRes = [];
        for (let rowData of mapList) {
            // 圖片確認清單
            const imgWrongNameList = [];
            // 檔案確認清單
            const fileWrongNameList = [];

            // 每筆rowData要建立的主要id
            // const mainSrcId = `${findPrefix}${mainMaxNum}`;
            const finalEntry = {
                srcId: await getReservedNewId(sheetName),
                classType: sheetName,
                graph: pattern,
                value: {},
            };

            // fixme: 一筆rowData如果有多筆URLEvent，這邊邏輯要改
            const imgURLHeader = Object.keys(rowData).find(
                (key) => key.indexOf(excelColKey.imageURL_hasURL) > -1
            );

            let urlInst = {};
            // "imageURL_hasURL"欄位有值才要新增URLEvent
            if (imgURLHeader && rowData[imgURLHeader]) {
                // 單筆rowData要額外連接的instance，e.g: URLEvent
                urlInst = {
                    srcId: "",
                    graph: pattern,
                    classType: "URLEvent",
                    value: {},
                };
            }

            for (let key of Object.keys(rowData)) {
                const colKeyID = key.split(STRING_SEPARATOR)[1];
                const findObj = processCol.find(
                    ({ excelColKey }) => excelColKey === colKeyID
                );
                if (findObj && rowData[key]) {
                    const val =
                        typeof rowData[key] === "string"
                            ? rowData[key].split(STRING_SEPARATOR)
                            : rowData[key];

                    if (colKeyID === excelColKey.imageURL_hasURL) {
                        const imgPath = imagePathDist[sheetName];
                        if (imgPathRef[0].pid.includes(imgPath)) {
                            const urlData = findObj.method(
                                val,
                                pattern,
                                imgPath,
                                sheetName
                            );

                            // 檔案名稱沒有換成file server現有檔名，表示沒有找到圖片
                            if (urlData.imageName[0] === val[0]) {
                                imgWrongNameList.push(val[0]);
                            }

                            urlInst.value = {
                                ...urlInst.value,
                                ...urlData,
                            };
                        }
                    } else if (
                        colKeyID === excelColKey.hasCopyrightStatus_hasURL
                    ) {
                        if (!isEmpty(urlInst)) {
                            urlInst.value = {
                                ...urlInst.value,
                                ...findObj.method(val, copyrightList),
                            };
                        }
                    } else if (colKeyID === excelColKey.label_Publication) {
                        // 找原文書ID
                        const pubId = pubList.data.find(
                            (el) => el.label === val[0]
                        )?.id;
                        if (pubId) {
                            finalEntry.value = {
                                ...finalEntry.value,
                                ...findObj.method(val, pubId),
                            };
                        }
                    } else if (
                        [
                            excelColKey.hasAuthor,
                            excelColKey.hasTranslator,
                        ].includes(colKeyID)
                    ) {
                        const tmpAuthorList = await getListWithParams(
                            { ds: pattern },
                            getDsPerOrgInfolist
                        )

                        const allID = val.map(
                            (nameStr) =>
                                tmpAuthorList.data.find(
                                    (str) => str.label === nameStr.split("_")[1]
                                )?.id
                        );

                        if (allID) {
                            finalEntry.value = {
                                ...finalEntry.value,
                                ...findObj.method(val, allID),
                            };
                        }

                        val.map((i)=>{
                            if ( colKeyID === excelColKey.hasEditor ) return;
                            const type = i.split("@").pop();
                            const evtType = classPrefix.find(
                                ({ prefix }) => prefix === type
                            )?.eventType;

                            const targetNum = i.split('_')[0]
                            const fId = tmpAuthorList.data.find(
                                (str) => str.label === i.split("_")[1]
                            )?.id

                            let nameValues;
                            let tmpAllName;
                            const nameArr = [];

                            const colKey = (clk) => {
                                switch (colKeyID) {
                                    case excelColKey.hasAuthor:
                                        return '作者發表名稱\nauthorName'
                                    case excelColKey.hasTranslator:
                                        return '譯者顯示名稱\ntranslatorName'
                                    default :
                                        return '作者發表名稱\nauthorName'
                                }
                            }

                            const tmpClk = colKey(colKeyID)
                            nameValues = rowData[tmpClk].split(STRING_SEPARATOR);
                            tmpAllName = nameValues.find(e=>e.includes(targetNum));
                            const enName = tmpAllName.split('_').pop();
                            const chName = tmpAllName.split('_')[1];
                            nameArr.push(enName);
                            nameArr.push(chName);

                            if(nameArr.length === 0) return;

                            nameArr?.map(async (name)=> {
                                const findlstIdx = name.lastIndexOf("@");
                                const tmpName =  `${name.slice(0, findlstIdx)}`;
                                const tmpOtherNameEntry = {
                                    classType: evtType,
                                    graph: pattern,
                                    srcId: fId,
                                    value: {
                                        otherName: tmpName,
                                    },
                                };

                                await generic20(tmpOtherNameEntry, () => {});
                            })
                        })
                    }
                    else if (
                        [
                            excelColKey.hasEditor,
                            excelColKey.hasPublisher
                        ].includes(colKeyID)
                    ) {
                        const tmpAuthorList = await getListWithParams(
                            { ds: pattern },
                            getDsPerOrgInfolist
                        )

                        const allID = val.map(
                            (nameStr) =>
                                tmpAuthorList.data.find(
                                    (str) => str.label === nameStr
                                )?.id
                        );

                        if (allID) {
                            finalEntry.value = {
                                ...finalEntry.value,
                                ...findObj.method(val, allID),
                            };
                        }

                    }
                    else if (
                        colKeyID === excelColKey.srcId_hasPlaceOfPublication
                    ) {
                        const geoColKey = [
                            excelColKey.geoLatitude_hasPlaceOfPublication,
                            excelColKey.geoLongitude_hasPlaceOfPublication,
                        ].map((str) =>
                            Object.keys(rowData).find(
                                (keyStr) =>
                                    keyStr.split(STRING_SEPARATOR)[1] === str
                            )
                        );
                        const findId = getLocIDObj(
                            rowData,
                            key,
                            geoColKey,
                            locList
                        );
                        if (findId?.id) {
                            finalEntry.value = {
                                ...finalEntry.value,
                                ...findObj.method(val, findId?.id),
                            };
                        }
                    } else if (
                        [excelColKey.hasLanguageOfWorkOrName,excelColKey.hasTranslationLanguage].includes(colKeyID)
                    ) {
                        finalEntry.value = {
                            ...finalEntry.value,
                            ...findObj.method(val, langList),
                        };
                    } else if (colKeyID === excelColKey.LiteraryGenre) {
                        finalEntry.value = {
                            ...finalEntry.value,
                            ...findObj.method(val, litGeneryList),
                        };
                    } else if (colKeyID === excelColKey.fileAvailableAt) {
                        const tmpObj = findObj.method(val, pattern);
                        tmpObj.fileAvailableAt.forEach((tmpURL) => {
                            const newfileName = tmpURL.split("/").pop();
                            if (val.includes(newfileName)) {
                                // 檔名相同，表示fileServer找不到已上傳檔案(對應名稱後面會加timestamp)
                                fileWrongNameList.push(newfileName);
                            }
                        });

                        finalEntry.value = { ...finalEntry.value, ...tmpObj };
                    } else {
                        finalEntry.value = {
                            ...finalEntry.value,
                            ...findObj.method(val),
                        };
                    }
                }
            }

            // 在file server對應資料夾有找到圖片，URLEventID才要增加
            if (!checkWrongList([imgWrongNameList])) {
                urlInst.srcId = await getReservedNewId("URLEvent");
                finalEntry.value = {
                    ...finalEntry.value,
                    hasURL: [{ ...urlInst }],
                };
            }

            // return res.status(200).send(finalEntry);
            if (checkWrongList([imgWrongNameList, fileWrongNameList])) {
                checkList = [
                    ...checkList,
                    ...[imgWrongNameList, fileWrongNameList].filter(
                        (tmpArr) => tmpArr.length > 0
                    ),
                ];
                allRes.push("failed");
            } else {
                // write database
                await generic20(finalEntry, (values) => {
                    if (values[0].status === 200) {
                        allRes.push("OK");
                    } else {
                        allRes.push("failed");
                    }
                });
            }
        }

        // 結束，確認有沒有要需要寄信告知操作者要更新的部分
        if (allRes.length === mapList.length) {
            if (checkWrongList(checkList)) {
                const tmpList = checkList.map((tmpArr) =>
                    tmpArr.join(STRING_SEPARATOR)
                );
                const checkListStr = tmpList.join(STRING_SEPARATOR);
                const customMsg = `請重新上傳以下圖片或檔案，並重新匯入該列資料\n${checkListStr}\n注意: 重新匯入須移除沒有問題的資料，避免重複匯入。`;
                // 上傳結束後更改檔名
                const newPath = getFinishFileName(req, "failed");
                fs.rename(filePath, newPath, () => {});

                emailToUser(
                    user,
                    filePath,
                    mailMessage.status.failure,
                    customMsg
                );
                return res.status(200).send("failed");
            } else {
                // 沒有錯誤再重新上傳資料
                if (allRes.every((res) => res === "OK")) {
                    // 上傳結束後更改檔名
                    const newPath = getFinishFileName(req, "finish");
                    fs.rename(filePath, newPath, () => {});

                    emailToUser(user, filePath, mailMessage.status.success);
                    return res.status(200).send("OK");
                } else {
                    // 上傳結束後更改檔名
                    const newPath = getFinishFileName(req, "failed");
                    fs.rename(filePath, newPath, () => {});

                    emailToUser(
                        user,
                        filePath,
                        mailHelper.mailMessage.status.failure
                    );
                    return res.status(200).send("failed");
                }
            }
        }
    } catch (err) {
        console.log("error:", err.message);
        let rtDbDataObj, rtUpdateRes;
        try {
            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }
            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }
        } catch (err) {
            console.log("err in exportMain", err.message);
        }
        // finally {
        //     // email 給使用者
        //     emailToUser(user, filePath, mailHelper.mailMessage.status.failure);
        // }
    }
};

module.exports = importMain;
