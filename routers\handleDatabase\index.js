const { RESPONSE_OK, ERROR_API_METHOD } = require("../../config/config");

const defaultBackupDB = "nmtldb.trig-backup.tar.gz";
const dbDownload = () => {
    console.log("dbDownload");
    return defaultBackupDB;
};

const dbStatus = () => {
    console.log("dbStatus");
    return "dbStatus";
};

const dbUpload = () => {
    console.log("dbUpload");
    return "dbUpload";
};

const dbApiMap = {
    download: dbDownload,
    status: dbStatus,
    upload: dbUpload,
};

const database = async (req, res) => {
    const { act } = req.params;

    if (dbApiMap.hasOwnProperty(act)) {
        return res.status(RESPONSE_OK).send(dbApiMap[act]());
    }
    return res.status(400).send(ERROR_API_METHOD);
};

module.exports = database;
