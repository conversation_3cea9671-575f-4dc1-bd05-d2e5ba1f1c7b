const fs = require("fs");
const JSZip = require("jszip");

const xlsx = require("node-xlsx");
const timerHelper = require("./timerHelper");
const errorHelper = require("./errorHelper");
const commonHelper = require("./commonHelper");
const { getSheetHeaderLabel } = require("../../handleAuth/apiObj");
//
const exportConfig = require("../../../config/config.export");
//
const fileHelper = {};

const dataObjHeadKey = "head";
const dataObjDataKey = "data";

// 取得檔案名稱
fileHelper.getFileNameAndKey = (file) => {
    const ts = Date.now();
    // const { graph, sheet, splitNum } = file || {};
    const database = exportConfig.pattern.getDatabase();
    const extension = exportConfig.pattern.extension;
    const zipExtension = exportConfig.pattern.zipExtension;

    // const fileKey = `${database}-${graph}-${sheet}-${splitNum}-${ts}`;
    const fileKey = `${database}-${ts}`;
    const fileName = `${fileKey}.${extension}`;
    const zipFileName = `${fileKey}.${zipExtension}`;

    return { fileKey, fileName, zipFileName };
};

// 取得提取檔案 url
// e.g.https://.......
fileHelper.getFileUrlPath = (fileName) => {
    const fileServerApiRoute = exportConfig.FILE_SERVER_DOWNLOAD_ROUTE;

    // 串接成 url
    // https://fs-root.daoyidh.com/ + nmtl/export/ + nmtl-1705235714737.zip
    return `${exportConfig.FILE_SERVER_BASE_URL}/${fileServerApiRoute}/${fileName}`;
};

// tsv to excel buffer
fileHelper.tsvToExcelBuffer = async (dataObj, splitNum, sheetName, graph) => {
    if (!(dataObjHeadKey in dataObj && dataObjDataKey in dataObj)) {
        return null;
    }
    // data: {
    //   data: [{}, {},...],
    //   head: [
    //         'Name_ID',       'Best_Known_Name',
    //         'Local_Name_ID', 'Name_Type',
    //         'Name',          'HKCAN_ID',
    //         'LCNAF',         'VIAF',
    //         'Wikidata',      'Year_of_Birth',
    //         'Year_of_Death', 'Remark_Name',
    //         'Remark_Year',   'Remark_Other'
    //   ]
    // }
    const startTime = new Date();
    timerHelper.timeStart("tsvToExcelBuffer", startTime);

    if (!Array.isArray(dataObj[dataObjHeadKey])) {
        return null;
    }

    const headers = dataObj[dataObjHeadKey];
    const headersLabel = getSheetHeaderLabel(sheetName);
    const fileName = `${graph}_${sheetName}_${commonHelper.getCurDate()}`;

    const sheetDatas = [];
    if (Array.isArray(dataObj[dataObjDataKey])) {
        let xlsxData = [headersLabel];
        // xlsxData.push(headers);

        // 提昇速度
        const subValObj = commonHelper
            .getSubValueProperty()
            .reduce((a, v) => ({ ...a, [v]: 1 }), {});

        dataObj[dataObjDataKey].forEach((dt, idx) => {
            const row = [];
            headers.forEach((hd) => {
                // subValue 的 property 要濾掉
                if (hd in subValObj) {
                    return;
                }

                if (hd in dt) {
                    row.push(dt[hd]);
                } else {
                    row.push(null);
                }
            });
            xlsxData.push(row);

            // 每 splitNum 個，就分割一個檔案，怕檔案太大無法打開。
            if ((idx + 1) % splitNum === 0) {
                // 建構 sheet name 以及 data
                const buffer = xlsx.build([
                    {
                        name: `${sheetName}_${idx - splitNum + 2}-${idx + 1}`,
                        data: xlsxData,
                    },
                ]);

                // 為每個檔案增加有意義的檔名
                sheetDatas.push({ fileName, buffer });
                xlsxData = [headersLabel];
            }
        });

        // 最後一個檔案，裡頭有除了 header 以外的值
        if (xlsxData.length > 1) {
            const sheets = sheetDatas.length;
            // 建構 sheet name 以及 data
            const buffer = xlsx.build([
                {
                    name: `${sheetName}_${sheets * splitNum + 1}-${
                        sheets * splitNum + xlsxData.length - 1
                    }`,
                    data: xlsxData,
                },
            ]);

            // 為每個檔案增加有意義的檔名
            sheetDatas.push({ fileName, buffer });
        }
    }
    // console.log("xlsxData", xlsxData);s
    timerHelper.timeStampEnd("tsvToExcelBuffer", startTime);
    return sheetDatas;
};

// 壓縮檔案
// return: node buffer
const zipFileToBuffer = async (dataArr, extension) => {
    if (!Array.isArray(dataArr)) {
        return null;
    }
    try {
        const zip = new JSZip();

        // 壓縮多個檔案
        for (const idx in dataArr) {
            const { fileName, buffer } = dataArr[idx];
            zip.file(`${idx}-${fileName}.${extension}`, buffer, {
                binary: true,
            });
        }

        const zipBuffer = await zip.generateAsync({
            type: "nodebuffer",
            compression: "DEFLATE",
        });
        if (zipBuffer) {
            return zipBuffer;
        } else {
            return null;
        }
    } catch (err) {
        return err;
    }
};

const STORE_FILE_MESSAGE = {
    success: "success",
    fail: "fail",
};

fileHelper.STORE_FILE_MESSAGE = STORE_FILE_MESSAGE;

// store file to path
// fileName: zip 裡面的 檔案: filename.xlsx
// fileDst: zip 檔案路徑: /mnt/nmtl-files/aaaa/filename.zip
fileHelper.writeFile = async (fileName, dataArr, fileDst, extension) => {
    // console.log(fileName, fileDst, extension);
    if (!Array.isArray(dataArr)) {
        return errorHelper.sendErrorCode(
            errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
        );
    }

    try {
        const startTime = new Date();
        timerHelper.timeStart("writeFile", startTime);

        const zipBuffer = await zipFileToBuffer(dataArr, extension);

        if (!(zipBuffer instanceof Error)) {
            // flag: 'w' => Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
            const err = await fs.promises.writeFile(fileDst, zipBuffer, {
                flag: "w",
            });
            if (err) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
                );
            } else {
                timerHelper.timeStampEnd("writeFile", startTime);
                return { status: STORE_FILE_MESSAGE.success };
            }
        } else {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
            );
        }
    } catch (err) {
        console.log("writeFile err.message", err.message);
        return err;
    }
};

module.exports = fileHelper;
