{"name": "tltc-api", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "test", "start": "cross-env NODE_ENV=production node server.js", "start:nmtl": "cross-env ENV=production_tltc NODE_ENV=production node server.js", "dev": "cross-env NODE_ENV=development nodemon server.js"}, "author": "", "license": "MIT", "dependencies": {"async": "^2.6.0", "axios": "^1.6.5", "base64url": "^3.0.1", "body-parser": "^1.18.2", "commander": "^2.15.1", "compression": "^1.7.4", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "express": "^4.16.3", "firebase": "^7.24.0", "firebase-admin": "^8.11.0", "is_js": "^0.9.0", "jsonfile": "^4.0.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "md5": "^2.3.0", "mongodb": "^3.6.5", "multer": "^1.4.5-lts.1", "node-schedule": "^1.3.3", "node-xlsx": "^0.23.0", "nodemailer": "^6.6.3", "os-utils": "0.0.14", "prettier": "^2.2.1", "process-top": "^1.2.0", "redis": "^4.0.4", "sparql-http-client": "^2.4.2", "stardog": "^3.0.0", "systeminformation": "^4.34.19", "zlib": "^1.0.5"}, "devDependencies": {"babel-eslint": "^10.0.3", "eslint": "5.16.0", "eslint-config-prettier": "^2.9.0", "eslint-config-standard": "^12.0.0-alpha.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-react": "^7.22.0", "eslint-plugin-standard": "^3.1.0", "gulp": "^4.0.2", "gulp-eslint": "^4.0.2", "gulp-nodemon": "^2.5.0"}}