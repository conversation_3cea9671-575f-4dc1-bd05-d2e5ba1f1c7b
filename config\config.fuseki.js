const configFuseki = {};

/*  URL backup
    http://localhost:3000
    http://localhost:5820
    http://*************:5820
    http://nmtl-db.daoyidh.com:5820
 */
if (process.env.NODE_ENV === "production") {
    const {
        db,
        url,
        user,
        password,
    } = require("/opt/private-config/tlsg-tltc-fuseki.json");
    configFuseki.db = db;
    configFuseki.url = url;
    configFuseki.user = user;
    configFuseki.password = password;
} else {
    // configFuseki.url = "http://localhost:5821";
    // 正式站
    // configFuseki.db = "nmtl";
    // configFuseki.url = "http://*************:5821";
    // 測試站
    configFuseki.db = "tltc2";
    configFuseki.url = "http://*************:5837";

    configFuseki.user = "dev";
    configFuseki.password = "6FRokdSWrVJjL0AiRgqtm1H89texnG6F2nY4EKlbQWB6j5aI";
}

console.log("[ENV] FUSEKI_DB", configFuseki.db);
console.log("[ENV] FUSEKI_URL", configFuseki.url);

module.exports = configFuseki;
