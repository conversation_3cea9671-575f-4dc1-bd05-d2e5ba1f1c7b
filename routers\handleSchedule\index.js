const { ERROR_LACK_OF_PARAMS } = require("../../config/config");
const { dailyUpdateCount } = require("./rtdbCount");

function handleSchedule(req, res) {
    /*
    const { pageNo, id, pageWords } = req.body;

    // The update data is existing or not.
    if (!pageNo || !id || !pageWords) {
        return res.status(400).send(ERROR_WRONG_PARAMS);
    }
*/
    // The API has to have the version.
    const { cron } = req.params;
    if (!cron) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    if (cron === "count") {
        dailyUpdateCount();
    }
    return res.status(200).send("Received Daily");
}

module.exports = handleSchedule;
