- npm install
- gulp nodemon

- nohup bash -c "gulp nodemon 2>&1 &"
- $ ps -aux | grep gulp
ptcweb    8507  0.1  0.0 1219092 51528 pts/0   Sl   22:43   0:00 gulp
- kill -9 8507

- $ ps -aux | grep node
ptcweb   11089  0.1  0.1 947792 72980 ?        Sl   00:18   0:00 /usr/bin/node src/server.js
- kill -9 11089

- Use PM2
`
$ cd /web/ptc-server
$ pm2 start src/server.js
$ NODE_ENV=production pm2 start src/server.js
`
