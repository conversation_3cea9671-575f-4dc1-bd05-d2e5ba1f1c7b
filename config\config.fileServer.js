const path = require("path");
const { getOs } = require("../commons/common");

// fixme: 固定圖片上傳路徑，物圖片只確認人物像(portrait)、著作圖片只確認書封(bookCover)
// e.g. /original/imgupload/portrait/tltc、/original/imgupload/portrait/ra...
const imagePathDist = {
    Person: "portrait",
    Publication: "bookCover",
};

const getDirectory = (windowPath, linuxPath) => {
    return getOs() === "windows" ? path.join(windowPath) : path.join(linuxPath);
};

const originDir = getDirectory(
    process.env.WINDOWS_UPLOAD_IMAGE_DIRECTORY,
    process.env.LINUX_UPLOAD_IMAGE_DIRECTORY
);

const originDocDir = getDirectory(
    process.env.WINDOWS_UPLOAD_DOCS_DIRECTORY,
    process.env.LINUX_UPLOAD_DOCS_DIRECTORY
);

const filePath = {
    readFile: `${process.env.FILE_SERVER_API_DOMAIN}/static/file/upload`,
    imageUploadPath: "imgupload",
};

module.exports = {
    imagePathDist,
    originDir,
    originDocDir,
    filePath,
};
