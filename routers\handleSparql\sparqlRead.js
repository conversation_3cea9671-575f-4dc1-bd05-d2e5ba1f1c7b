const { verifyToken } = require("../handleAuth/auth");
const { getApi } = require("../handleAuth/apiObj");
const {
    ERROR_NO_PARAM_VER,
    ERROR_LACK_OF_PARAMS,
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_API_VER_NOT_EXIST,
    ERROR_WRONG_PARAMS,
    DEFAULT_MAX_LIMIT,
    DEFAULT_OFFSET,
    RESPONSE_OK,
    RESPONSE_BAD_REQUEST,
    REDUNDANT_PREFIX,
} = require("../../config/config");
const {
    replaceQueryParams,
    map2SparqlLang,
} = require("./services/common/common");
const {
    doMakeApiQuery,
    doMakeCountQuery,
} = require("./services/common/sparql-common");

async function sparqlRead(req, res, next) {
    const authToken = req.headers.authorization;

    // The API has to have the version.
    const { ver, lang } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }
    const language = map2SparqlLang(lang);

    // The API method is not correct.
    let apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(400).send(ERROR_API_METHOD);
    }

    // API method with redundant prefix "_".
    // ex: _ra/nmtl/footer/1.0?limit=-1&offset=0
    //     _ra is redundant, has to remove it.
    if (apiMethod[0] === REDUNDANT_PREFIX) {
        apiMethod = apiMethod.substring(apiMethod.indexOf("/") + 1);
    }
    // console.log(apiMethod, ver, language);

    // The API method doesn't exist.
    const apiDef = getApi(apiMethod);
    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    // The parameters for API are available.
    const methodParams = apiVer.params;
    if (Object.keys(req.query).length !== methodParams.length) {
        return res.status(400).send(ERROR_WRONG_PARAMS);
    }

    const foundParams = methodParams.filter((param) => {
        if (!req.query.hasOwnProperty(param)) {
            return true;
        }
    });

    if (foundParams.length > 0 && foundParams) {
        const lackParams = foundParams.join(", ");
        return res
            .status(400)
            .send({ error: `${ERROR_LACK_OF_PARAMS.error} ${lackParams}` });
    }

    // limit and offset
    const limit = req.query.hasOwnProperty("limit")
        ? req.query.limit
        : DEFAULT_MAX_LIMIT;
    const offset = req.query.hasOwnProperty("offset")
        ? req.query.offset
        : DEFAULT_OFFSET;

    let apiQuery = replaceQueryParams(
        apiVer.query,
        req.query,
        apiVer.key,
        language
    );
    if (apiQuery === "") {
        return res.status(400).send(ERROR_WRONG_PARAMS);
    }

    // console.log(apiQuery);
    await verifyToken(authToken)
        .then(async () => {
            await Promise.all([
                doMakeApiQuery(apiQuery, limit, offset),
                doMakeCountQuery(apiQuery),
            ])
                .then((value) => {
                    // console.log(value);
                    let resObj = {};
                    value.forEach((v) => {
                        resObj = Object.assign(resObj, v);
                    });
                    req.daoyiData = resObj;
                    req.daoyiStatus = RESPONSE_OK;
                    next();
                })
                .catch((err) => {
                    req.daoyiData = err.message;
                    req.daoyiStatus = RESPONSE_BAD_REQUEST;
                    next();
                });
        })
        .catch((error) => {
            console.log(error);
            req.daoyiData = error;
            req.daoyiStatus = RESPONSE_BAD_REQUEST;
            next();
        });
}

module.exports = sparqlRead;
