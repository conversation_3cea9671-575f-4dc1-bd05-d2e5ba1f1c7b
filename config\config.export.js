const storeDriver = require("../routers/handleSparql/services/sparql/storeDriver");

const EXTENSION = {
    xlsx: "xlsx",
    zip: "zip",
    tsv: "tsv",
};

let FIRE_BASE_RTDB_ROOT = "/database/local";
let API_ROOT = process.env.NMTL_API_NODE;
const AI_API = process.env.NMTL_ATAI_API_NODE;

if (process.env.NODE_ENV === "production") {
    FIRE_BASE_RTDB_ROOT = "/database/production";
} else if (process.env.NODE_ENV === "development") {
    FIRE_BASE_RTDB_ROOT = "/database/development";
}

// 向 file server 抓檔案
const FILE_SERVER_BASE_URL =
    process.env.FILE_SERVER_API_DOMAIN || "https://fs-root.daoyidh.com";

// api route
const FILE_SERVER_DOWNLOAD_ROUTE = "nmtl/export";

const exportConfig = {
    FilePath: "/mnt/nmtl-static-files/nmtl/export",
    getLabel: `${API_ROOT}/zh-tw/POST/required/label/1.0?limit=-1&offset=0&ids=`,
    getAllIdPath: (getClassList, ds) =>
        `${API_ROOT}/zh-tw/${getClassList}?limit=-1&offset=0&ds=${ds}`,
    getSpoPath: (getTable2, ds) =>
        `${API_ROOT}/zh-tw/POST/${getTable2}?limit=-1&offset=0&ds=${ds}&ids=`,
    getSubValSpo: (ds) =>
        `${API_ROOT}/zh-tw/POST/required/subvalue/1.0?limit=-1&offset=0&ds=${ds}&ids=`,
    DATE_EVENT: ["DateEvent"],
    DATE_EVENT_PREFIX: "DAE",
    LABEL: "label",
    IMAGE_NAME: "imageName",
    IMAGE_PATH: "imagePath",
    IMAGE_URL: "imageURL",
    // !!SubValues 必須與後台 sheetCrudHelper.js 裡的設定一致!!
    SubValues: {
        // property 的第一個值必須有值，不然會造成 Event 在後台操作被移除
        hasSource: { type: "Source", property: ["label", "externalLinks"] },
        hasURL: {
            type: "URLEvent",
            property: [
                "imageName",
                "imagePath",
                "imageFrom",
                "hasCopyrightStatus",
            ],
        },
        format: {
            type: "Format",
            property: [
                "length",
                "width",
                "height",
                "thickness",
                "diameter",
                "weight",
            ],
        },
    },
    MAX_POST_IDS: 1000,
    // GRAPH: GRAPH,
    EXTENSION: EXTENSION,
    FILE_SERVER_BASE_URL: FILE_SERVER_BASE_URL,
    FILE_SERVER_DOWNLOAD_ROUTE: FILE_SERVER_DOWNLOAD_ROUTE,
    pattern: {
        getDatabase: storeDriver.getDbName,
        extension: EXTENSION.xlsx,
        zipExtension: EXTENSION.zip, // 壓縮檔
        dir: `export/${storeDriver.getDbName}`, // 存放檔案資料夾
    },
    firebaseRtDb: {
        readWritePath: `${FIRE_BASE_RTDB_ROOT}/export`,
    },
    exportForAI: AI_API,
};

module.exports = exportConfig;
