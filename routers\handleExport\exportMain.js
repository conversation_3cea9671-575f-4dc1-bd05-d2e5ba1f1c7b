const fs = require("fs");
const path = require("path");
//
const { getMailApi } = require("../handleAuth/apiObj");
//
const exportConfig = require("../../config/config.export");
//
const firebaseHelper = require("./helpers/firebaseHelper");
const fileHelper = require("./helpers/fileHelper");
const mailHelper = require("./helpers/mailHelper");
const dbHelper = require("./helpers/dbHelper");
const errorHelper = require("./helpers/errorHelper");
const axios = require("axios");

//
const { getFileUrlPath } = fileHelper;

// 依據 extension 轉檔
const fileConvert = async (dataArr, extension, splitNum) => {
    if (!Array.isArray(dataArr)) {
        return null;
    }

    let outArr = [];
    for (const dItem of dataArr) {
        const { head, data, sheet, graph } = dItem;
        if (extension === exportConfig.EXTENSION.xlsx) {
            const dataConvert = await fileHelper.tsvToExcelBuffer(
                { head, data },
                splitNum,
                sheet,
                graph
            );
            outArr = outArr.concat(dataConvert);
        }
    }

    if (outArr) {
        return outArr;
    }
    return null;
};

// email 給使用者
const emailToUser = (user, fileUrlPath, status) => {
    if (status && status in mailHelper.mailMessage.user) {
        const sendMail = mailHelper.mailMessage.user[status](user, fileUrlPath);
        mailHelper.sendToUser(sendMail);
    }
};

// email 給管理者
const emailToManager = (user, fileUrlPath, status) => {
    const managerInfo = getMailApi("mail-config", "manager");

    if (managerInfo && status && status in mailHelper.mailMessage.manager) {
        const sendMail = mailHelper.mailMessage.manager[status](
            user,
            fileUrlPath,
            managerInfo
        );
        mailHelper.sendToUser(sendMail);
    }
};

const postToAI = async (user, fileUrlPath, status, msg) => {
    const option = {
        user,
        fileUrlPath,
        status: status
            ? mailHelper.mailMessage.status.success
            : mailHelper.mailMessage.status.failure,
        msg,
    };

    await axios
        .post(exportConfig.exportForAI, option)
        .catch((error) => console.error(error));
};
// 執行 database 匯出流程
const exportMain = async ({ user, file, fileName, zipFileName, fileKey }) => {
    const { splitNum, downloadForAI } = file;
    const fileUrlPath = getFileUrlPath(zipFileName);
    const rtDbPath = exportConfig.firebaseRtDb.readWritePath;

    try {
        // 1.database 匯出
        const dbExportArr = await dbHelper.databaseExport(file);

        // console.log(dbExportArr);
        if (
            dbExportArr instanceof Error ||
            (dbExportArr && dbExportArr.error)
        ) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.DB_EXPORT_ERROR.name
            );
        }

        // 2.轉檔
        // [{fileName, buffer}...]
        const fileConvertArr = await fileConvert(
            dbExportArr,
            exportConfig.EXTENSION.xlsx,
            splitNum
        );

        if (!fileConvertArr) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.FILE_CONVERT_ERROR.name
            );
        }

        // console.log(fileConvertArr);
        // 儲存檔案路徑
        const targetPath = exportConfig.FilePath;

        //
        // 為該路徑建立資料夾
        fs.mkdirSync(targetPath, { recursive: true }, () => {});
        const filePath = path.join(targetPath, zipFileName);

        // 3.儲存檔案
        const storeFile = await fileHelper.writeFile(
            fileName,
            fileConvertArr,
            filePath,
            exportConfig.EXTENSION.xlsx
        );

        // 4.email 通知 及 更新 firebase realtime db
        if (storeFile instanceof Error || storeFile === null) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
            );
        }

        if (fileUrlPath) {
            let rtDbDataObj, rtUpdateRes;

            // 先取得 realtime database 設定檔
            rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log("rtDbDataObj", rtDbDataObj);

            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }
            // firebase 更新
            if (rtDbDataObj) {
                const updateVal = firebaseHelper.getRtDbUpdate4DownloadReady({
                    data: rtDbDataObj,
                    user,
                    fileKey,
                    downloadUrl: fileUrlPath,
                });
                // 更新 realtime database 的資訊
                rtUpdateRes = await firebaseHelper.rtDbUpdate(
                    rtDbPath,
                    updateVal
                );
            }
            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }

            if (downloadForAI && Boolean(downloadForAI)) {
                // 更新阿台資料庫
                await postToAI(
                    user,
                    fileUrlPath,
                    true,
                    "File is ready to download"
                );
            } else {
                // 通知使用者
                emailToUser(
                    user,
                    fileUrlPath,
                    mailHelper.mailMessage.status.success
                );
            }

            // 通知管理者
            // emailToManager(
            //     user,
            //     fileUrlPath,
            //     mailHelper.mailMessage.status.success
            // );
        }

        // firebase 更新
    } catch (err) {
        console.log("exportMain error", err.message);
        let errObj = errorHelper.ERROR_CODE.DEFAULT;
        if (err.message in errorHelper.ERROR_CODE) {
            errObj = errorHelper.ERROR_CODE[err.message];
        }
        let rtDbDataObj, rtUpdateRes;
        try {
            // 先取得 realtime database 設定檔
            rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log("rtDbDataObj", rtDbDataObj);

            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }
            // firebase 更新
            if (rtDbDataObj) {
                const updateVal = firebaseHelper.getRtDbUpdate4DownloadFail({
                    data: rtDbDataObj,
                    fileKey,
                    errorMessage: errObj.message,
                });
                // FIXME:: 未來需補上將 updateVal 中已完成的 fileKey 整個刪除，以免越來越多
                // 更新 realtime database 的資訊
                rtUpdateRes = await firebaseHelper.rtDbUpdate(
                    rtDbPath,
                    updateVal
                );
            }
            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }
        } catch (err) {
            console.log("err in exportMain", err.message);
        } finally {
            // 使用者提取的檔案路徑
            const fileUrlPath = getFileUrlPath(fileName);

            if (downloadForAI && Boolean(downloadForAI)) {
                // 更新阿台資料庫
                await postToAI(user, fileUrlPath, false, "File export failure");
            } else {
                // 通知使用者
                emailToUser(
                    user,
                    fileUrlPath,
                    mailHelper.mailMessage.status.failure
                );
            }

            // 通知管理者
            // emailToManager(
            //     user,
            //     fileUrlPath,
            //     mailHelper.mailMessage.status.failure
            // );
        }
    }
};

module.exports = exportMain;
