const {
    instanceRandomID,
    eventPrefix,
    classPrefix,
} = require("../classPrefix");
const {
    getApiProperty,
    getApiRelation,
} = require("../../../../handleAuth/apiObj");
const defines = require("../properties/bindIdDefine");
const unihanStrokes = require("./unihan_strokes.json");

exports.isRandomId = (type) => {
    return instanceRandomID.find(
        (cp) => cp.eventType.toLowerCase() === type.toLowerCase()
    );
};

exports.isEventType = (type) => {
    return eventPrefix.find(
        (cp) => cp.eventType.toLowerCase() === type.toLowerCase()
    );
};

exports.isSupportType = (type) => {
    return classPrefix.find(
        (cp) => cp.eventType.toLowerCase() === type.toLowerCase()
    );
};

exports.getTypeFromPrefix = (srcId) => {
    return classPrefix.find((cp) => {
        if (cp.prefix === "") {
            return null;
        }
        return srcId.startsWith(cp.prefix);
    });
};

exports.getPrefixFromType = (type) => {
    return classPrefix.find(
        (cp) => type.toLowerCase() === cp.eventType.toLowerCase()
    );
};

// return array
exports.getPropertyBetween = (srcType, dstType) => {
    return getApiProperty().filter(
        (ap) => ap.domain === srcType && ap.range === dstType
    );
};

// return unique array
exports.getTypeRangeFromProperty = (classType, property) => {
    return getApiProperty()
        .filter((ap) => ap.property === property)
        .filter((ap) => ap.domain === classType)
        .map((p) => `${p.type}${defines._PropertySplitter}${p.range}`)
        .reduce(
            (unique, item) =>
                unique.includes(item) ? unique : [...unique, item],
            []
        );
};

// { invert: 'isFatherOf',
//   relationLabel: '有父',
//   invertLabel: '是...的父親',
//   relation: 'hasFather' },
exports.findRelation = (value) => {
    const found = getApiRelation().find((rel) => {
        return rel.relation === value || rel.relationLabel === value;
    });
    return found ? found.relation : null;
};

exports.findInverseRelation = (value) => {
    const found = getApiRelation().find((rel) => {
        return rel.relation === value || rel.invert === value;
    });
    return found
        ? found.invert === value
            ? found.relation
            : found.invert
        : null;
};

// return true/false
exports.isCorrectProperty = (subject, property) => {
    const found = getApiProperty().filter(
        (ap) => ap.property === property && ap.domain === subject
    );
    return found && found.length > 0;
};

exports.getStroke = (name) => {
    const unicode = name.charCodeAt(0);
    const unicodeStr = name.charCodeAt(0).toString();

    if (unicodeStr in unihanStrokes) {
        return unihanStrokes[unicode];
    }
    return "9999";
};

// const getPrefixByDomain = (predicate, apiProperty) => {
//     // FamilyRelation or Relation
//     const fPredicate = apiProperty.find((ap) => ap.property === predicate);
//     return classPrefix.find((cp) => cp.eventType === fPredicate.domain);
// };
// const getPrefixByRange = (predicate, apiProperty) => {
//     const fPredicate = apiProperty.find((ap) => ap.property === predicate);
//     return classPrefix.find((cp) => cp.eventType === fPredicate.range);
// };
