const { baseIri } = require("../rdf");
const { isSubValue } = require("../../common/common");
const {
    getPrefixFromType,
    getPropertyBetween,
    findInverseRelation,
    isCorrectProperty,
} = require("./common");
const { myProperty } = require("./myProperty");
const defines = require("./bindIdDefine");

const baseUri = defines.baseUri;

exports.processSubValue = (bindId, classType, subValue) => {
    const value = subValue;
    const subObj = { subWhere: "", processed: "", subRemove: "" };

    if (!subValue) {
        return subObj;
    }

    Object.keys(value).forEach((key) => {
        let dstValueArr = value[key];

        // hasXXX: [{ srcId: "", classType: "", value:{ OOO:"aaaaaa" } }, {}]
        // key: hasXXX
        if (!isSubValue(dstValueArr)) {
            return;
        }

        const valProperty = key.split("__")[0];
        dstValueArr.forEach((dstValue, idx) => {
            // sub value
            const { srcId: subId, classType: subClassType } = dstValue;

            let hasProperty = getPropertyBetween(classType, subClassType).map(
                (rel) => rel.property
            );
            // 確定帶進來的 property 是對的
            if (hasProperty.indexOf(valProperty) < 0) {
                if (!hasProperty || hasProperty.length !== 1) {
                    console.error(
                        "hasProperty has more than two relations::",
                        hasProperty,
                        classType,
                        subClassType
                    );
                    return;
                }
            }
            const subPrefix = getPrefixFromType(subClassType);

            const subBindId = `${defines._NewSubRandomId}_${subClassType}${idx}_${valProperty}`;
            if (subId === "") {
                // create new sub
                const newBnode = `?bnode_${subClassType}${idx}`;
                subObj.subWhere += `BIND(REPLACE(STR(BNODE()), ":", "") AS ${newBnode}) .`;
                subObj.subWhere += `BIND(IRI(CONCAT("${baseUri}", "${subPrefix.prefix}_", ${newBnode})) AS ?${subBindId}) .`;
                subObj.processed += myProperty(dstValue, `?${subBindId}`, true);
            } else {
                subObj.subWhere += `BIND(IRI(${baseIri(
                    subId
                )}) AS ?${subBindId}) .`;
                subObj.processed += myProperty(
                    dstValue,
                    `?${subBindId}`,
                    false
                );
            }

            // inverse 關係
            const inverseOpObj = findInverseRelation(valProperty);
            if (inverseOpObj) {
                // 極特殊情況，inverse 關係並不屬於此 Class
                // 如：Person hasSpecialty SpecialtyEvent
                //    SpecialtyEvent hasSpecialty Specialty
                // 但是 Specialty 並沒有 inverse 關係 SpecialtyEvent
                // 以 hasSpecialty 皆可找到 inverse 關係 isSpecialtyOf
                if (isCorrectProperty(subClassType, inverseOpObj)) {
                    subObj.processed += `?${subBindId} ${baseIri(
                        inverseOpObj
                    )} ?${bindId} .`;
                }
            }

            // 自動更新lastModified
            subObj.processed += `?${subBindId} ${
                defines._LastModified
            } '''${new Date().getTime()}''' .`;
            subObj.subRemove += `?${subBindId} ${defines._LastModified} ?lastModified_${subClassType}${idx}_${valProperty} .`;
            subObj.subWhere += `OPTIONAL{?${subBindId} ${defines._LastModified} ?lastModified_${subClassType}${idx}_${valProperty} .}`;

            subObj.processed += `?${subBindId} a ${baseIri(subClassType)} .`;
            // DUMMY Type 不需要 assign
            if (bindId) {
                subObj.processed += `?${bindId} ${baseIri(
                    valProperty
                )} ?${subBindId} .`;
            }
        });
    });

    return subObj;
};
