const { RESPONSE_OK } = require("../../config/config");
const { getSubCollections } = require("../handleAuth/dlFirebaseDb");

const firebaseInfo = async (req, res) => {
    const { info } = req.params;
    getSubCollections(info)
        .then((dlMs) => {
            return res.status(RESPONSE_OK).send(dlMs);
        })
        .catch((error) => {
            return res.status(RESPONSE_OK).send(error);
        });
};

module.exports = firebaseInfo;
