const firebase = require("../../config/config.firebase");
const firestoreDb = firebase.firestore();

const apiListener = (callback) => {
    const colRef = firestoreDb.collection("api");
    colRef.onSnapshot(
        (docSnapshot) => {
            console.log(`Received doc snapshot: ${docSnapshot}`);
            colRef
                .get()
                .then((result) => {
                    const apiObj = {};
                    result.docs.forEach((x) => {
                        apiObj[x.id] = x.data();
                    });
                    callback(apiObj);
                })
                .catch((error) => {
                    console.log(`Encountered error: ${error}`);
                    callback(error);
                });
        },
        (err) => {
            console.log(`Encountered error: ${err}`);
            callback(err);
        }
    );
};

module.exports = apiListener;
