const { namespace } = require("../rdf");

const defines = {
    baseUri: namespace.base,

    _bindAddId: "bindAddId",
    _bindDelId: "bindDelId",
    _bindUpdId: "bindUpdId",
    _NewRandomId: "randomId",

    // dummy type, 只處理 SubValue，如：新舊組織表單
    _DUMMY: "DUMMY",

    // sub value id
    _subUpdId: "subUpdId",
    _NewSubRandomId: "subRandomId",

    // 定義特殊 property 以創建 instances
    _CreateInstance: "label",

    // 自動產生與更新 lastModified
    _LastModified: "nmtl:lastModified",

    // 自動產生與更新 nameStroke
    _NameStroke: "nmtl:nameStroke",

    // 資料放在 _Value 裡
    _Value: "value",
    _ValueSTARTS: "label_",

    _PropertySplitter: "___",
};

module.exports = defines;
