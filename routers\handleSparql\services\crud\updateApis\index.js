const { generic20 } = require("./lib/updateGeneric");
const { mergeInstances20 } = require("./lib/updateMergeInstances");
const { copyInstances20 } = require("./lib/updateCopyInstances");
const {
    updateCopyInstancesOnlyId20,
} = require("./lib/updateCopyInstancesOnlyId");
const { updateFs20 } = require("./lib/updateFs");

const index = {
    genealogy: { "1.0": generic20, required: ["graph"] }, // deprecated
    generic: { "2.0": generic20, required: ["srcId"] },
    mergeInstances: { "2.0": mergeInstances20, required: ["srcId"] },
    copyInstances: { "2.0": copyInstances20, required: ["srcId"] },
    copyInstancesOnlyId: {
        "2.0": updateCopyInstancesOnlyId20,
        required: ["srcId"],
    },
    /* #Vincent#, 20241227, for tlsgfs.nmtl.gov.tw */
    updateFs: { "2.0": updateFs20, required: ["graph"] },
};

module.exports = index;
