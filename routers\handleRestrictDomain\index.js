const is = require("is_js");
const {
    ALLOWED_DOMAIN,
    ALLOWED_GOV_DOMAIN,
    ALLOWED_ORIGINS,
    ALLOWED_DL_IPS,
    RESPONSE_OK,
    ERROR_FORBIDDEN_HTML,
} = require("../../config/config");
const { getClientIp } = require("../handlePublicIp/daoyi-request-ip");

module.exports = {
    httpRestrictDomain(req, res, next) {
        // 必須由相同的 domain 發出
        if (req && req.headers && req.headers.origin) {
            const { origin } = req.headers;
            // domain to pass
            if (
                origin.endsWith(ALLOWED_DOMAIN) ||
                origin.endsWith(ALLOWED_GOV_DOMAIN)
            ) {
                return next();
            } else if (ALLOWED_ORIGINS.indexOf(origin) > -1) {
                return next();
            }
        }
        return res.status(RESPONSE_OK).send(ERROR_FORBIDDEN_HTML);
    },
    async httpRestrictIp(req, res, next) {
        const clientIp = await getClientIp(req);
        if (ALLOWED_DL_IPS.indexOf(clientIp) > -1) {
            // okay! PASS
            return next();
        }
        if (is.ipv6(clientIp)) {
            // ipv6 pass, 可能在同網域
            return next();
        }
        return res.status(RESPONSE_OK).send(ERROR_FORBIDDEN_HTML);
    },
};
